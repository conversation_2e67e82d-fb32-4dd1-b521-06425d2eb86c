<?php /*a:4:{s:77:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\choujiang\edit.html";i:1753713548;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\copyright.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>添加活动</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
	<style>
	#ggtable .layui-input{ display:inline;height:30px}
	#ggtable .layui-btn{ margin-top:-3px;margin-left:1px}
	</style>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header">
					<?php if($info['id']): ?>编辑活动<?php else: ?>添加活动<?php endif; ?>
					<i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i>
				</div>
				<div class="layui-card-body" pad15>
					<!-- <blockquote class="layui-elem-quote"></blockquote> -->
					<div class="flex">
						<div class="layui-form flex1" lay-filter="" style="padding-bottom:20px">
							<input type="hidden" name="info[id]" value="<?php echo $info['id']; ?>"/>
							<input type="hidden" name="info[type]" value="<?php echo $info['type']; ?>"/>

							活动设置<hr/>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>活动名称：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" class="layui-input" name="info[name]" value="<?php echo $info['name']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
							</div>
							<?php if($info['type'] == 'dzp' || $info['type'] == 'ggk' || $info['type'] == 'yyl'): ?>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px">顶部图片：</label>
								<input type="hidden" name="info[banner]" id="banner" lay-verType="tips" class="layui-input" value="<?php echo $info['banner']; ?>">
								<button style="float:left;" type="button" class="layui-btn layui-btn-primary" upload-input="banner" upload-preview="bannerPreview" onclick="uploader(this)">上传图片</button>
								<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：640×320像素，png格式</div>
								<div id="bannerPreview" style="float:left;padding-top:10px;padding-left:140px;clear: both;">
									<div class="layui-imgbox" style="width:100px;"><div class="layui-imgbox-img"><img src="<?php echo $info['banner']; ?>"/></div></div>
								</div>
							</div>
							<?php endif; if($info['type'] == 'dzp' || $info['type'] == 'ggk' || $info['type'] == 'zjd'): ?>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px">背景图片：</label>
								<input type="hidden" name="info[bgpic]" id="bgpic" lay-verType="tips" class="layui-input" value="<?php echo $info['bgpic']; ?>">
								<button style="float:left;" type="button" class="layui-btn layui-btn-primary" upload-input="bgpic" upload-preview="bgpicPreview" onclick="uploader(this)">上传图片</button>
								<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：750*1340像素</div>
								<div id="bgpicPreview" style="float:left;padding-top:10px;padding-left:140px;clear: both;">
									<div class="layui-imgbox" style="width:100px;"><div class="layui-imgbox-img"><img src="<?php echo $info['bgpic']; ?>"/></div></div>
								</div>
							</div>
							<?php endif; ?>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:100px">背景色：</label>
								<div class="layui-input-inline">
									<input type="text" name="info[bgcolor]" value="<?php echo $info['bgcolor']; ?>" class="layui-input">
								</div>
								<div class="_colorpicker" style="float:left"></div>
							</div>

							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>活动规则：</label>
								<div class="layui-input-inline" style="width:300px">
									<textarea class="layui-textarea" name="info[guize]" lay-verify="required" lay-verType="tips"><?php echo $info['guize']; ?></textarea>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>开始时间：</label>
								<div class="layui-input-inline">
									<input type="text" id="starttime" name="info[starttime]" value="<?php echo date('Y-m-d H:i:s',$info['starttime']); ?>" class="layui-input">
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>结束时间：</label>
								<div class="layui-input-inline">
									<input type="text" id="endtime" name="info[endtime]" value="<?php echo date('Y-m-d H:i:s',$info['endtime']); ?>" class="layui-input">
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>每人参与的总次数：</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" name="info[pertotal]" value="<?php echo $info['pertotal']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux"></div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>每人每天可参与次数：</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" name="info[perday]" value="<?php echo $info['perday']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux"></div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>分享增加抽奖次数：</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" name="info[shareaddnum]" value="<?php echo $info['shareaddnum']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux">每分享一次增加多少抽奖机会，0为不增加抽奖次数</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>每天最多分享次数：</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" name="info[sharedaytimes]" value="<?php echo $info['sharedaytimes']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux">0为不限制，每天分享次数多于此设置不再增加抽奖次数</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>最多分享总次数：</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" name="info[sharetimes]" value="<?php echo $info['sharetimes']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux">0为不限制，分享次数多于此设置不再增加抽奖次数</div>
							</div>
							<?php if(getcustom('plug_tengrui')): ?>
	                        <div class="layui-form-item">
	                            <label class="layui-form-label" style="width:100px">认证<?php echo t('会员'); ?>：</label>
	                            <div class="layui-input-inline">
	                                <input type="radio" name="info[is_rzh]" value="0" <?php if(!$info['id'] || $info['is_rzh']==0): ?>checked<?php endif; ?> title="否"/>
	                                <input type="radio" name="info[is_rzh]" value="1" <?php if($info['id'] && $info['is_rzh']==1): ?>checked<?php endif; ?> title="是"/>
	                            </div>
	                        </div>
	                        <div class="layui-form-item">
	                            <label class="layui-form-label" style="width:100px"><?php echo t('会员'); ?>关系：</label>
	                            <div class="layui-input-inline" style="width:500px">
	                            <input type="radio" name="info[relation_type]" value="-1" <?php if(!$info['id'] || $info['relation_type']==-1): ?>checked<?php endif; ?> title="所有"/>
	                                <input type="radio" name="info[relation_type]" value="0" <?php if($info['id'] && $info['relation_type']==0): ?>checked<?php endif; ?> title="业主"/>
	                                <input type="radio" name="info[relation_type]" value="1" <?php if($info['id'] && $info['relation_type']==1): ?>checked<?php endif; ?> title="家属"/>
	                                <input type="radio" name="info[relation_type]" value="2" <?php if($info['id'] && $info['relation_type']==2): ?>checked<?php endif; ?> title="租户"/>
	                                <input type="radio" name="info[relation_type]" value="3" <?php if($info['id'] && $info['relation_type']==3): ?>checked<?php endif; ?> title="买断"/>
	                                <input type="radio" name="info[relation_type]" value="4" <?php if($info['id'] && $info['relation_type']==4): ?>checked<?php endif; ?> title="租用"/>
	                            </div>
	                        </div>
	                        <div class="layui-form-item">
	                            <label class="layui-form-label" style="width:100px">一户仅限一次：</label>
	                            <div class="layui-input-inline">
	                                <input type="radio" name="info[house_status]" value="0" <?php if($info['id'] && $info['house_status']==0): ?>checked<?php endif; ?> title="否"/>
	                                <input type="radio" name="info[house_status]" value="1" <?php if(!$info['id'] || $info['house_status']==1): ?>checked<?php endif; ?> title="是"/>
	                            </div>
	                            <div class="layui-form-mid layui-word-aux" style="margin-left:10px;">需是认证用户，且同一个小区的同一户只限一人购买，即一户仅限一次</div>
	                        </div>
	                        <div class="layui-form-item">
	                            <label class="layui-form-label" style="width:100px">授权小区：</label>
	                            <div class="layui-input-inline" style="width:500px">
	                                <input type="radio" name="info[group_status]" value="0" <?php if($info['group_status']==0): ?>checked<?php endif; ?> title="关闭" lay-filter="group_status"/>
	                                <input type="radio" name="info[group_status]" value="1" <?php if($info['group_status']==1): ?>checked<?php endif; ?> title="开启" lay-filter="group_status"/>
	                            </div>
	                            <div class="group_status1 fwtypeExtend" style="clear:both;<?php if($info['group_status']!=1): ?>display:none<?php endif; ?>">
	                                <label class="layui-form-label" style="width:100px"></label>
	                                <div class="layui-input-inline" style="width:auto">
	                                    <table id="setgroupdiv"  class="layui-table" style="width:500px">
	                                        <thead>
	                                        <tr>
	                                            <th>小区ID</th>
	                                            <th>小区名称</th>
	                                            <th>操作</th>
	                                        </tr>
	                                        </thead>
	                                        <?php if($groupdata): foreach($groupdata as $gk=>$gv): ?>
	                                        <tr class="grouplisttr"><td><?php echo $gv['id']; ?></td><td><?php echo $gv['fullName']; ?></td><td><button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="delGroup(this,<?php echo $gv['id']; ?>)">删除</button></td></tr>
	                                        <?php endforeach; ?>
	                                        <?php endif; ?>
	                                        <tr id="groupaddtr">
	                                            <td colspan="3"><button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="showChooseGroup()">添加</button></td>
	                                        </tr>
	                                    </table>
	                                    <input type="hidden" name="info[group_ids]" value="<?php echo $info['group_ids']; ?>"/>
	                                </div>
	                            </div>
	                            <script>
	                            var chooseGroupLayer;
	                            function showChooseGroup(){
	                                chooseGroupLayer = layer.open({type:2,title:'选择分组',content:"<?php echo url('MemberTrGroup/choosegroup'); ?>",area:['1000px','600px'],shadeClose:true});
	                            }
	                            function chooseGroup(fid,fname){
	                                layer.close(chooseGroupLayer);
	                                var group_ids = [];
	                                var isadd = 0;
	                                $('.grouplisttr').each(function(){
	                                    var thisfid = $(this).find('td:eq(0)').html();
	                                    if(thisfid == fid){
	                                        isadd = 1
	                                        dialog('该分组已添加过了');
	                                    }
	                                    group_ids.push(thisfid)
	                                })
	                                if(isadd == 0){
	                                    group_ids.push(fid)
	                                    $("input[name='info[group_ids]']").val(group_ids.join(','));
	                                    $('#groupaddtr').before('<tr class="grouplisttr"><td>'+fid+'</td><td>'+fname+'</td><td><button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="delGroup(this,'+fid+')">删除</button></td></tr>');
	                                }
	                                console.log(group_ids)
	                            }
	                            function delGroup(obj,fid){
	                                $(obj).parent().parent().remove();
	                                var group_ids = [];
	                                $('.grouplisttr').each(function(){
	                                    group_ids.push($(this).find('td:eq(0)').html())
	                                })
	                                $("input[name='info[group_ids]']").val(group_ids.join(','));
	                            }
	                            </script>
	                        </div>
	                        <?php endif; ?>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>参与条件：</label>
								<div class="layui-input-inline" style="width:500px">
									<input type="checkbox" name="info[gettj][]" value="-1" title="所有人" <?php if(in_array('-1',$info['gettj'])): ?>checked<?php endif; ?>/>
									<input type="checkbox" name="info[gettj][]" value="0" title="关注用户" <?php if(in_array('0',$info['gettj'])): ?>checked<?php endif; ?>/>
									<?php foreach($memberlevel as $v): ?>
									<input type="checkbox" name="info[gettj][]" value="<?php echo $v['id']; ?>" title="<?php echo $v['name']; ?>" <?php if(in_array($v['id'],$info['gettj'])): ?>checked<?php endif; ?>/>
									<?php endforeach; ?>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>参与范围：</label>
								<div class="layui-input-inline">
									<input type="radio" name="info[fanwei]" value="1" title="开启" <?php if($info['fanwei']==1): ?>checked<?php endif; ?> lay-filter="fanweiset"/>
									<input type="radio" name="info[fanwei]" value="0" title="关闭" <?php if(!$info['id'] || $info['fanwei']==0): ?>checked<?php endif; ?> lay-filter="fanweiset"/>
								</div>
							</div>
							<div class="layui-form-item" id="fanweiset" <?php if(!$info['id'] || $info['fanwei']==0): ?>style="display:none"<?php endif; ?>>
								<label class="layui-form-label" style="width:100px">限制范围：</label>
								<input type="hidden" name="zoom" value="">
								<div class="form-group"  style="width:800px;height: 600px;margin:0 0 10px 130px;position: relative">
										<link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
										<script src="https://webapi.amap.com/maps?v=1.4.0&key=cc6545949691fe61692f4036ab2f0336&plugin=AMap.PolyEditor,AMap.CircleEditor"></script>
										<script src="//webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
										<script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
										<div id="container" class="map" > </div>
										<div class="button-group" style="bottom: 0px;right: 15px;">
											<input type="button" class="button" value="开始选取坐标范围" id="start_position" />
											<input type="button" class="button" value="结束选取坐标范围" id="stop_position" />
										</div>
								</div>
								<div class="layui-form-mid" style="margin-left:130px;">中心点坐标</div>
								<div class="layui-input-inline" style="width: 100px;">
									<input type="text" name="info[fanwei_lng]" class="layui-input" value="<?php echo $info['fanwei_lng']; ?>">
								</div>
								<div class="layui-form-mid">,</div>
								<div class="layui-input-inline" style="width: 100px;">
									<input type="text" name="info[fanwei_lat]" class="layui-input" value="<?php echo $info['fanwei_lat']; ?>">
								</div>
								<div class="layui-form-mid">范围半径</div>
								<div class="layui-input-inline" style="width: 100px;">
									<input type="text" name="info[fanwei_range]" class="layui-input" value="<?php echo $info['fanwei_range']; ?>">
								</div>
								<div class="layui-form-mid">米</div>
							</div>

							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>消耗类型：</label>
								<div class="layui-input-inline" style="width: 500px">
									<input type="radio" name="info[use_type]" value="1" title="消耗<?php echo t('积分'); ?>" <?php if(!$info['id'] || $info['use_type']==1): ?>checked<?php endif; ?> lay-filter="set_use_type"/>
									<input type="radio" name="info[use_type]" value="2" title="消耗<?php echo t('余额'); ?>" <?php if($info['id'] && $info['use_type']==2): ?>checked<?php endif; ?> lay-filter="set_use_type"/>
									<input type="radio" name="info[use_type]" value="3" title="消耗<?php echo t('贡献值'); ?>" <?php if($info['id'] && $info['use_type']==3): ?>checked<?php endif; ?> lay-filter="set_use_type"/>
								</div>
							</div>
							<div class="layui-form-item" id="usescore" <?php if($info['id'] && $info['use_type']!=1): ?>style="display:none"<?php endif; ?>>
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>消耗<?php echo t('积分'); ?>：</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" name="info[usescore]" value="<?php echo $info['usescore']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<?php if(getcustom('plug_tengrui')): ?>
								<div class="layui-form-mid layui-word-aux">第一次抽奖需要多少<?php echo t('积分'); ?>，0为免费抽奖</div>
								<?php endif; if(!getcustom('plug_tengrui')): ?>
								<div class="layui-form-mid layui-word-aux">每次抽奖需要多少<?php echo t('积分'); ?>，0为免费抽奖</div>
								<?php endif; ?>
							</div>
							<?php if(getcustom('plug_tengrui')): ?>
							<div class="layui-form-item" id="add_num_score" <?php if($info['id'] && $info['use_type']!=1): ?>style="display:none"<?php endif; ?>>
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>增加次数消耗<?php echo t('积分'); ?>：</label>
								<div class="layui-input-inline">
									<input type="number" class="layui-input" name="info[add_num_score]" value="<?php echo $info['add_num_score']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux">每增加一次抽奖需要多消耗多少<?php echo t('积分'); ?></div>
							</div>
							<div class="layui-form-item" id="limit_score" <?php if($info['id'] && $info['use_type']!=1): ?>style="display:none"<?php endif; ?>>
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>封顶<?php echo t('积分'); ?>：</label>
								<div class="layui-input-inline">
									<input type="number" class="layui-input" name="info[limit_score]" value="<?php echo $info['limit_score']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux">例如,设置封顶上限<?php echo t('积分'); ?>为100分一次，增加满100积分后面再抽就是100分一次</div>
							</div>
							<?php endif; ?>
							<div class="layui-form-item"  id="usemoney" <?php if(!$info['id'] || $info['use_type']!=2): ?>style="display:none"<?php endif; ?>>
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>消耗<?php echo t('余额'); ?>：</label>
								<div class="layui-input-inline">
									<input type="text" class="layui-input" name="info[usemoney]" value="<?php echo $info['usemoney']; ?>" lay-verify="required" lay-verType="tips"/>
								</div>
								<div class="layui-form-mid layui-word-aux">每次抽奖需要多少<?php echo t('余额'); ?>，0为免费抽奖</div>
							</div>

	  						<div class="layui-form-item"  id="usecontribution" <?php if(!$info['id'] || $info['use_type']!=3): ?>style="display:none"<?php endif; ?>>
								  <label class="layui-form-label" style="width:110px"><span class="redstar">*</span>消耗<?php echo t('贡献值'); ?>：</label>
								  <div class="layui-input-inline">
									  <input type="text" class="layui-input" name="info[usecontribution]" value="<?php echo $info['usecontribution']; ?>" lay-verify="required" lay-verType="tips"/>
								  </div>
								  <div class="layui-form-mid layui-word-aux">每次抽奖需要多少<?php echo t('贡献值'); ?>，0为免费抽奖</div>
							  </div>


							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"><span class="redstar">*</span>状 态：</label>
								<div class="layui-input-inline">
									<input type="radio" name="info[status]" value="1" title="开启" <?php if(!$info['id'] || $info['status']==1): ?>checked<?php endif; ?>/>
									<input type="radio" name="info[status]" value="0" title="关闭" <?php if($info['id'] && $info['status']==0): ?>checked<?php endif; ?>/>
								</div>
							</div>
							奖项设置<hr/>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:10px"></label>
								<table class="layui-table" style="width:900px">
									<thead>
										<tr>
											<th style="width:80px">奖项</th>
											<!-- <th style="width:170px">奖项名称</th> -->
											<th style="width:140px">奖品类型</th>
											<th style="width:210px">奖品名称</th>
											<th style="width:120px">奖品图片</th>
											<th style="width:90px">奖品数量</th>
											<th style="width:90px">中奖概率</th>
											<th style="width:90px">已出奖数</th>
										</tr>
									</thead>
									<tbody>
										<?php $__FOR_START_1425445047__=1;$__FOR_END_1425445047__=11;for($i=$__FOR_START_1425445047__;$i < $__FOR_END_1425445047__;$i+=1){ $jxtp = 'j'.$i.'tp'; ?>
										<tr>
											<td>奖项<?php echo $i; ?></td>
											<!-- <td><input type="text" value="<?php echo $info['j'.$i.'mc']; ?>" name="info[j<?php echo $i; ?>mc]" class="layui-input"/></td> -->
											<td>
												<select name="info[j<?php echo $i; ?>tp]" lay-filter="jxtp">
													<option value="1" <?php if($info[$jxtp]==1): ?>selected<?php endif; ?>>奖品名称</option>
													<option value="2" <?php if($info[$jxtp]==2): ?>selected<?php endif; ?>>微信红包</option>
													<option value="3" <?php if($info[$jxtp]==3): ?>selected<?php endif; ?>><?php echo t('优惠券'); ?></option>
													<option value="4" <?php if($info[$jxtp]==4): ?>selected<?php endif; ?>><?php echo t('积分'); ?></option>
													<option value="5" <?php if($info[$jxtp]==5): ?>selected<?php endif; ?>><?php echo t('余额'); ?></option>
												</select>
											</td>
											<td>
												<div class="flex">
													<input type="text" value="<?php echo $info['j'.$i.'mc']; ?>" name="info[j<?php echo $i; ?>mc]" class="layui-input flex1" style="vertical-align:middle;display:inline"/>
													<span class="mid" style="height:38px;line-height:38px;<?php if($info[$jxtp]!=2): ?>display:none<?php endif; ?>">&nbsp;元</span>
													<button class="layui-btn layui-btn-primary selectcoupon" onclick="selectcoupon(<?php echo $i; ?>)" style="margin-left:5px;padding:0 12px;<?php if($info[$jxtp]!=3): ?>display:none<?php endif; ?>">选择</button>
												</div>
											</td>
											<td>
												<input type="hidden" name="info[j<?php echo $i; ?>pic]" id="j<?php echo $i; ?>pic" value="<?php echo $info['j'.$i.'pic']; ?>"/>
												<div class="layui-imgbox" style="width:35px;height:35px;float:left" id="j<?php echo $i; ?>pic_preview">
													<a class="layui-imgbox-close" href="javascript:void(0)" onclick="removejxpic(this)" title="删除"><i class="layui-icon layui-icon-close-fill"></i></a>
													<span class="layui-imgbox-img" ><img src="<?php echo $info['j'.$i.'pic']; ?>"></span>
												</div>
												<button style="float:left;padding:0 12px" class="layui-btn  layui-btn-primary" onclick="uploader(this)" upload-input="j<?php echo $i; ?>pic" upload-preview="j<?php echo $i; ?>pic_preview" type="button">上传</button>
											</td>
											<td><input type="text" value="<?php echo $info['j'.$i.'sl']; ?>" name="info[j<?php echo $i; ?>sl]" class="layui-input jsl"/></td>
											<td><input type="text" value="" id="j<?php echo $i; ?>gl" class="layui-input" readonly style="width:60px;display:inline"/> %</td>
											<td><input type="text" class="layui-input" readonly value="<?php echo $info['j'.$i.'yj']; ?>"></td>
										</tr>
										<?php } ?>
										<tr>
											<td>不中奖</td>
											<td>&nbsp;</td>
											<td>
												<div class="flex">
													<input type="text" value="<?php echo $info['j0mc']; ?>" name="info[j0mc]" class="layui-input flex1" style="display:inline"/>
												</div>
											</td>
											<td>
												<input type="hidden" name="info[j0pic]" id="j0pic" value="<?php echo $info['j0pic']; ?>"/>
												<div class="layui-imgbox" style="width:35px;height:35px;float:left" id="j0pic_preview">
													<a class="layui-imgbox-close" href="javascript:void(0)" onclick="removejxpic(this)" title="删除"><i class="layui-icon layui-icon-close-fill"></i></a>
													<span class="layui-imgbox-img" ><img src="<?php echo $info['j0pic']; ?>"></span>
												</div>
												<button style="float:left;padding:0 12px" class="layui-btn layui-btn-primary" onclick="uploader(this)" upload-input="j0pic" upload-preview="j0pic_preview" type="button">上传</button>
											</td>
											<td><input type="text" lay-verify="required" lay-verType="tips" value="<?php echo $info['j0sl']; ?>" name="info[j0sl]" class="layui-input jsl"/></td>
											<td><input type="text" value="" id="j0gl" class="layui-input" readonly style="width:60px;display:inline"/> %</td>
											<td><input type="text" class="layui-input" readonly value="<?php echo $info['j0yj']; ?>"></td>
										</tr>
									</tbody>
								</table>
								<div class="layui-form-mid layui-word-aux" style="clear:both;margin-left:40px">请依次填写奖项；微信红包金额必须在1.00到200.00元之间,随机红包格式:1.5-2.0,请确保微信商户平台有足够可用金额；奖项为<?php echo t('优惠券'); ?>时请确保设置的<?php echo t('优惠券'); ?>库存充足</div>
							</div>
							<script>
							var selectcouponLayer;
							function selectcoupon(jx){
								selectcouponLayer = layer.open({type:2,title:'选择<?php echo t('优惠券'); ?>',content:"<?php echo url('Coupon/choosecoupon'); ?>/args/"+jx,area:['1000px','600px'],shadeClose:true});
							}
							function choosecoupon(res,args){
								console.log(args)
								layer.close(selectcouponLayer);
								$("input[name='info[j"+args+"mc]']").val(res.id)
							}
							function removejxpic(obj){
								$(obj).parent().find('img').attr('src','');
								$(obj).parent().parent().find('input').val('');
							}
							</script>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px">红包场景：</label>
								<div class="layui-input-inline" style="width:300px">
									<select name="info[scene_id]">
										<option value="">默认</option>
										<option value="PRODUCT_1" <?php if($info['scene_id'] == 'PRODUCT_1'): ?>selected<?php endif; ?>>商品促销</option>
										<option value="PRODUCT_2" <?php if($info['scene_id'] == 'PRODUCT_2'): ?>selected<?php endif; ?>>抽奖</option>
										<option value="PRODUCT_3" <?php if($info['scene_id'] == 'PRODUCT_3'): ?>selected<?php endif; ?>>虚拟物品兑奖</option>
										<option value="PRODUCT_4" <?php if($info['scene_id'] == 'PRODUCT_4'): ?>selected<?php endif; ?>>企业内部福利</option>
										<option value="PRODUCT_5" <?php if($info['scene_id'] == 'PRODUCT_5'): ?>selected<?php endif; ?>>渠道分润</option>
										<option value="PRODUCT_6" <?php if($info['scene_id'] == 'PRODUCT_6'): ?>selected<?php endif; ?>>保险回馈</option>
										<option value="PRODUCT_7" <?php if($info['scene_id'] == 'PRODUCT_7'): ?>selected<?php endif; ?>>彩票派奖</option>
										<option value="PRODUCT_8" <?php if($info['scene_id'] == 'PRODUCT_8'): ?>selected<?php endif; ?>>税务刮奖</option>
									</select>
								</div>
								<div class="layui-form-mid layui-word-aux">有微信红包奖励且红包金额大于200或者小于1元时选择，选择场景时请确保商户平台[现金红包设置]内已申请相应的场景配额</div>
							</div>

							兑奖信息设置<hr/>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:100px">添加表单项：</label>
								<div class="layui-input-inline" style="width:auto;margin-top:5px">
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('input','','',1)"><i class="icon-plus"></i>单行输入</button>
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('textarea','','',1)"><i class="icon-plus"></i>多行输入</button>
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('radio','','',1)"><i class="icon-plus"></i>单项选择</button>
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('checkbox','','',1)"><i class="icon-plus"></i>多项选择</button>
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('selector','','',1)"><i class="icon-plus"></i>普通选择</button>
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('time','','',1)"><i class="icon-plus"></i>时间选择</button>
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('date','','',1)"><i class="icon-plus"></i>日期选择</button>
									<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('region','','',1)"><i class="icon-plus"></i>省市区选择</button>
									<!-- <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('switch','','',1)"><i class="icon-plus"></i>开关选择</button> -->
									<!-- <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addelement('upload','','',1)"><i class="icon-plus"></i>上传图片</button> -->
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:100px">表单项列表：</label>
								<div class="layui-input-inline" style="width:auto;margin-top:-7px;min-height:100px">
									<table class="layui-table" style="width:800px" id="ggtable">
										<thead>
										<tr>
											<th style="width:120px">字段类型</th>
											<th style="width:180px">字段名称</th>
											<th style="width:180px">字段内容</th>
											<th style="width:80px">是否必填</th>
											<th style="width:150px">操作</th>
										</tr>
										</thead>
										<tbody id="datatable">

										</tbody>
									</table>
								</div>
							</div>
							分享设置<hr/>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px">分享标题：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" class="layui-input" name="info[sharetitle]" value="<?php echo $info['sharetitle']; ?>"/>
								</div>
								<div class="layui-form-mid layui-word-aux">默认为活动名称</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px">分享图标：</label>
								<input type="hidden" name="info[sharepic]" id="sharepic" lay-verType="tips" class="layui-input" value="<?php echo $info['sharepic']; ?>">
								<button style="float:left;" type="button" class="layui-btn layui-btn-primary" upload-input="sharepic" upload-preview="sharepicPreview" onclick="uploader(this)">上传图片</button>
								<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：400*400像素</div>
								<div id="sharepicPreview" style="float:left;padding-top:10px;padding-left:140px;clear: both;">
									<div class="layui-imgbox" style="width:100px;"><div class="layui-imgbox-img"><img src="<?php echo $info['sharepic']; ?>"/></div></div>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px">分享描述：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" class="layui-input" name="info[sharedesc]" value="<?php echo $info['sharedesc']; ?>"/>
								</div>
								<div class="layui-form-mid layui-word-aux"></div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px">分享链接：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" class="layui-input" name="info[sharelink]" value="<?php echo $info['sharelink']; ?>"/>
								</div>
								<div class="layui-form-mid layui-word-aux" style="margin-left:140px">默认为活动链接，设置后发送给朋友或分享到朋友圈，点击后会跳转到此链接地址</div>
							</div>

							<div class="layui-form-item">
								<label class="layui-form-label" style="width:110px"></label>
								<div class="layui-input-block">
									<button class="layui-btn" lay-submit lay-filter="formsubmit">提 交</button>
								</div>
							</div>
						</div>
						<div>
							<div class="dsn-phone">
								<div class="dsn-phone-left"></div>
								<div class="dsn-phone-center">
									<div class="dsn-phone-top"></div>
									<div class="dsn-phone-main">
										<div id="editor">
											<div class="dsn-mod dsn-topbar dsn-mod-nohover" style="color:#fff;background:#000;height:60px">
												<div style="float:left;width:100%;font-size:12px">
													<div style="float:left;width:30%">&nbsp;<i class="fa fa-signal"></i> wechat <i class="fa fa-wifi"></i></div>
													<div style="float:left;text-align:center;width:40%">12:00</div>
													<div style="float:left;text-align:right;width:30%">100% <i class="fa fa-battery-full"></i>&nbsp;</div>
												</div>
												<div style="float:left;width:98%;margin:2px 1% 0 1%;">
													<div style="float:left;width:30%">&nbsp;</div>
													<div style="float:left;text-align:center;width:40%;font-size:16px;height:27px;line-height:27px" id="name_preview"><?php echo $info['name']; ?></div>
												</div>
											</div>
											<div id="editor-content" style="overflow:hidden">
												<img src="/static/img/<?php echo $info['type']; ?>/preview.png" style="width:100%">
											</div>
										</div>
									</div>
									<div class="dsn-phone-bottom"></div>
								</div>
								<div class="dsn-phone-right"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
    </div>
  </div>
	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script>
	layui.form.on('radio(group_status)',function(data){
        if(data.value == 1){
            $('.group_status1').show();
        }else{
            $('.group_status1').hide();
        }
    })
	layui.form.on('radio(set_use_type)',function(data){
		if(data.value=="1"){
			$('#usescore').show();
			$("#add_num_score").show();
			$("#limit_score").show();
			$('#usemoney').hide();
			$("#usecontribution").hide();
		}
		if(data.value=="2"){
			$('#usescore').hide();
			$("#add_num_score").hide();
			$("#limit_score").hide();
			$('#usemoney').show();
			$("#usecontribution").hide();
		}

		console.log(data.value,6666)
		if(data.value=="3"){
			//usecontribution
			$("#usecontribution").show();
			$('#usemoney').hide();
			$('#usescore').hide();
			$("#add_num_score").hide();
			$("#limit_score").hide();
		}

	});
	layui.form.on('radio(fanweiset)',function(data){
		if(data.value == '0'){
			$('#fanweiset').hide();
		}else{
			$('#fanweiset').show();
		}
	})
	$("input[name='info[name]']").bind("input change propertychange",function(event){
		$('#name_preview').text($(this).val())
	})
	//日期时间选择器
	layui.laydate.render({
		elem: '#starttime',
		type: 'datetime',
		trigger: 'click'
	});
	layui.laydate.render({
		elem: '#endtime',
		type: 'datetime',
		trigger: 'click'
	});

	layui.form.on('select(jxtp)',function(data){
		console.log(data)
		$(data.elem).parent().next().find('input').val('');
		if($(data.elem).val()==1){
			$(data.elem).parent().next().find('input').attr('placeholder','请填写奖品名称');
			$(data.elem).parent().next().find('.mid').hide();
			$(data.elem).parent().next().find('.selectcoupon').hide();
		}if($(data.elem).val()==2){
			$(data.elem).parent().next().find('input').attr('placeholder','请填写红包金额');
			$(data.elem).parent().next().find('.mid').show();
			$(data.elem).parent().next().find('.selectcoupon').hide();
		}if($(data.elem).val()==3){
			$(data.elem).parent().next().find('input').attr('placeholder','填写<?php echo t('优惠券'); ?>ID');
			$(data.elem).parent().next().find('.mid').hide();
			$(data.elem).parent().next().find('.selectcoupon').show();
		}if($(data.elem).val()==4){
			$(data.elem).parent().next().find('input').attr('placeholder','填写<?php echo t('积分'); ?>数量');
			$(data.elem).parent().next().find('.mid').hide();
			$(data.elem).parent().next().find('.selectcoupon').hide();
		}if($(data.elem).val()==5){
			$(data.elem).parent().next().find('input').attr('placeholder','填写<?php echo t('余额'); ?>金额');
			$(data.elem).parent().next().find('.mid').hide();
			$(data.elem).parent().next().find('.selectcoupon').hide();
		}
	})
	layui.form.on('submit(formsubmit)', function(obj){
		var field = obj.field
		//console.log(field);return;
		var index = layer.load();
		$.post("<?php echo url('save'); ?>",field,function(data){
			layer.close(index);
			dialog(data.msg,data.status);
			if(data.status == 1){
				setTimeout(function(){
					parent.layer.closeAll();
					parent.tableIns.reload()
				},1000)
			}
		})
	})
	function sjgl(){
		//获取所有奖项的数量总和
		var sum_sl = 0;
		$(".jsl").map(function(){
			if($(this).val()){
				sum_sl += parseInt($(this).val());
			}
		});
		console.log(sum_sl)

		//遍历奖项input标签中重写对应的概率input标签里的value
		$(".jsl").map(function(){
			var gl = parseInt((parseInt($(this).val())/sum_sl)*10000)/100;//保留两位小数
			if ($(this).val()) {
				$(this).parent().next().find("input").val(gl);
			}

		});
	}
	sjgl();
	//奖品数量input标签blur时，计算所有奖项的概率
	$(".jsl").bind('keyup blur',function(){
		sjgl();
	})


	var dhdata = <?php echo $info['formcontent']; ?>;
	$(function(){
		for(var i=0;i<dhdata.length;i++){
			addelement(dhdata[i].key,dhdata[i].val1,dhdata[i].val2,dhdata[i].val3)
		}
	});
	var ekey = 0;
	//添加元素
	function addelement(type,val1,val2,val3){
		if(type=='input'){
			var name = '单行输入';
		}else if(type=='textarea'){
			var name = '多行输入';
		}else if(type=='radio'){
			var name = '单项选择';
		}else if(type=='checkbox'){
			var name = '多项选择';
		}else if(type=='selector'){
			var name = '普通选择';
		}else if(type=='time'){
			var name = '时间选择';
		}else if(type=='date'){
			var name = '日期选择';
		}else if(type=='region'){
			var name = '省市区选择';
		}else if(type=='switch'){
			var name = '开关选择';
		}else if(type=='upload'){
			var name = '上传图片';
		}
		var addhtml = '';
		addhtml += '<tr>'
		addhtml += '<td>'+name+'<input type="hidden" name="datatype['+ekey+']" value="'+type+'"/></td>'
		addhtml += '<td><input class="layui-input" type="text" style="width:120px" name="dataval1['+ekey+']" value="'+val1+'" placeholder="请输入字段名称"></td>'
		addhtml += '<td>'
		if(type=='input'){
			addhtml += '<input class="layui-input" type="text" style="width:200px" name="dataval2['+ekey+']" value="'+val2+'" placeholder="请输入提示信息">'
		}else if(type=='textarea'){
			addhtml += '<input class="layui-input" type="text" style="width:200px" name="dataval2['+ekey+']" value="'+val2+'" placeholder="请输入提示信息">'
		}else if(type=='radio' || type=='checkbox' || type=='selector'){
			if(val2){
				for(var i=0;i<val2.length;i++){
					addhtml += '<div><input class="layui-input" type="text" style="width:180px" name="dataval2['+ekey+'][]" value="'+val2[i]+'" placeholder="请输入选项名称">';
					if(i==0){
						addhtml += '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addxuanxiang(this,'+ekey+')"><i class="fa fa-plus"></i></button></div>'
					}else{
						addhtml += '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="$(this).parent().remove()"><i class="fa fa-minus"></i></button></div>'
					}
				}
			}else{
				addhtml += '<div><input class="layui-input" type="text" style="width:180px" name="dataval2['+ekey+'][]" value="" placeholder="请输入选项名称">';
				addhtml += '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addxuanxiang(this,'+ekey+')"><i class="fa fa-plus"></i></button></div>'
			}
		}else if(type == 'time'){
			addhtml += '选择时间';
			//addhtml += '<div><input class="layui-input" type="text" style="width:200px" name="dataval2['+ekey+'][0]" value="'+(val2[0]?val2[0]:'00:00')+'" placeholder=\'开始时间，格式为"hh:mm"\'>';
			//addhtml += '<div><input class="layui-input" type="text" style="width:200px" name="dataval2['+ekey+'][1]" value="'+(val2[1]?val2[1]:'23:59')+'" placeholder=\'结束时间，格式为"hh:mm"\'>';
		}else if(type == 'date'){
			addhtml += '选择日期';
			//addhtml += '<div><input class="layui-input" type="text" style="width:200px" name="dataval2['+ekey+'][0]" value="'+(val2[0]?val2[0]:'1970-01-01')+'" placeholder=\'开始日期，格式为"YYYY-MM-DD"\'>';
			//addhtml += '<div><input class="layui-input" type="text" style="width:200px" name="dataval2['+ekey+'][1]" value="'+(val2[1]?val2[1]:'2050-01-01')+'" placeholder=\'结束日期，格式为"YYYY-MM-DD"\'>';
		}else if(type == 'region'){
			addhtml += '选择省市区';
		}else if(type == 'switch'){
			addhtml += '开启和关闭';
		}else if(type == 'upload'){
			addhtml += '<input class="layui-input" type="text" style="width:200px" name="dataval2['+ekey+']" value="'+val2+'" placeholder="请输入提示信息">'
		}
		addhtml += '</td>'
		//addhtml += '<td><select style="width:60px" name="dataval3['+ekey+']"><option value="1">是</option><option value="0" '+(val3==0?'selected="selected"':'')+'>否</option></select></td>'
		addhtml += '<td><input type="checkbox" name="dataval3['+ekey+']" value="1" lay-skin="switch" lay-text="开启|关闭" '+(val3==1?'checked':'')+'></td>'
		addhtml += '<td>'
		addhtml += '	<button title="删除" type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="$(this).parent().parent().remove()"><i class="fa fa-remove"></i></button>'
		addhtml += '	<button title="上移" type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="toup(this)"><i class="fa fa-arrow-up"></i></button>'
		addhtml += '	<button title="下移" type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="todown(this)"><i class="fa fa-arrow-down"></i></button>'
		addhtml += '</td>'
		addhtml += '</tr>'
		//alert(addhtml)
		$('#datatable').append(addhtml);
		layui.form.render('checkbox');
		ekey++;
	}
	//添加选项
	function addxuanxiang(obj,ekey){
		$(obj).parent().parent().append('<div><input class="layui-input" type="text" style="width:180px" name="dataval2['+ekey+'][]" value="" placeholder="请输入选项名称"><button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="$(this).parent().remove()"><i class="fa fa-minus"></i></button></div>');
	}
	</script>

	<script>
	var lng=$("input[name='info[fanwei_lng]']").val();
	var lat=$("input[name='info[fanwei_lat]']").val();
	var range=$("input[name='info[fanwei_range]']").val();

	var lnglat='';//设置的坐标
	if(lng !="" && lat !=""){
		lnglat=[lng,lat];
	}
	if(range==0){
		range=2000;
	}

	var circleisopen=false;//是否已经打开圆形编辑
	//初始化地图参数
	var editor={};
	var  map = new AMap.Map("container", {
		resizeEnable: true,//是否监控地图容器尺寸变化，默认值为false
		dragEnable: true,//是否允许拖拽地图
		keyboardEnable: false,//是否允许键盘平移
		doubleClickZoom: false,//是否允许双击放大地图
		scrollWheel:true,//是否允许鼠标滚轮操作地图
		center:lnglat,
		zoom: 13 //地图显示的缩放级别
	});

	var marker = new AMap.Marker({
		map: map,
		position: lnglat
	});

	//在地图上绘制覆盖物
	editor._circle=(function(){
		var circle = new AMap.Circle({
				center: lnglat,// 圆心位置
				radius: range, //半径
				strokeColor: "#4e73f1", //线颜色
				strokeOpacity: 1, //线透明度
				strokeWeight: 3, //线粗细度
				fillColor: "#4e73f1", //填充颜色
				fillOpacity: 0.35,//填充透明度
		});
		circle.setMap(map);
		return circle;
	})();
	if(lng !="" && lat !=""){
		map.setFitView();//根据地图上添加的覆盖物分布情况，自动缩放地图到合适的视野级别
	}
	editor._circleEditor= new AMap.CircleEditor(map, editor._circle);

	//初始化地图选点
	AMapUI.loadUI(['misc/PositionPicker'], function(PositionPicker) {
		var positionPicker = new PositionPicker({
				mode: 'dragMarker',
				map: map
		});

		//开始选取坐标
		$('#start_position').click(function () {
				positionPicker.start(lnglat);
				//坐标点为空时不打开圆形编辑
			 if(lnglat !=''){
					 editor._circleEditor.open(lnglat);
					 circleisopen=true;
			 }
			//map.setStatus({dragEnable: true});//是否允许拖拽地图
			//map.setStatus({keyboardEnable: true});//是否允许键盘平移
		})

		//结束选取坐标
		$('#stop_position').click(function () {
				//map.setStatus({dragEnable: false});//是否允许拖拽地图
				//map.setStatus({keyboardEnable: false});//是否允许键盘平移
				//if(editor._circle.getRadius()<(parseInt($('#fixed_km').val()))*1000){
				//		tip.msgbox.err("配送半径不能小于"+parseInt($('#fixed_km').val())+"公里");
				//		return;
				//}

				map.panTo(lnglat);
				positionPicker.stop();
				editor._circleEditor.close();
				map.setFitView();//根据地图上添加的覆盖物分布情况，自动缩放地图到合适的视野级别

				$("input[name='info[fanwei_lng]']").val(lnglat.lng);
				$("input[name='info[fanwei_lat]']").val(lnglat.lat);
				$("input[name='info[fanwei_range]']").val(editor._circle.getRadius());
				//$("input[name='zoom']").val(map.getZoom());
		});
		positionPicker.on('success', function(positionResult) {
				lnglat=positionResult.position;
				marker.setPosition(lnglat); //更新点标记位置
				editor._circle.setCenter(lnglat);//更新中心点坐标

				//获取当前坐标后再打开圆形编辑
				if(circleisopen==false){
						editor._circleEditor.open(lnglat);
				}
		});
	});
	//设置地图圆的半径
	function setRadius(radius) {
		editor._circle.setRadius(radius*1000);
		map.setFitView();
	}
	</script>
	
</body>
</html>
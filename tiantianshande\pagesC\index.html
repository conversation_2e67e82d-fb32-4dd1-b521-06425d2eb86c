<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运动健康小助手</title>
    <script src="./flexible.js"></script>
    <style>
        /* 基础样式重置 */
        * {
            box-sizing: border-box;
            flex-shrink: 0;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
        }

        input {
            background-color: transparent;
            border: 0;
        }

        button {
            margin: 0;
            padding: 0;
            border: 1px solid transparent;
            outline: none;
            background-color: transparent;
            cursor: pointer;
        }

        button:active {
            opacity: 0.6;
        }

        /* Flex布局工具类 */
        .flex-col {
            display: flex;
            flex-direction: column;
        }

        .flex-row {
            display: flex;
            flex-direction: row;
        }

        .justify-between {
            display: flex;
            justify-content: space-between;
        }

        .justify-center {
            display: flex;
            justify-content: center;
        }

        .align-center {
            display: flex;
            align-items: center;
        }

        /* 页面主容器 */
        .page {
            background: linear-gradient(180deg, rgba(237, 234, 255, 1) 0%, rgba(239, 236, 255, 1) 100%);
            position: relative;
            width: 375px;
            height: auto;
            min-height: 100vh;
            overflow: visible;
            margin: 0 auto;
            padding: 10px 10px 80px;
            box-sizing: border-box;
        }

        /* 头部区域 */
        .header {
            width: 100%;
            max-width: 355px;
            height: 60px;
            margin: 0 auto 20px;
            position: relative;
        }

        .header-status {
            background-color: rgba(50, 51, 53, 1);
            width: 22px;
            height: 15px;
            margin-top: 10px;
        }

        .header-signal {
            border-radius: 50%;
            width: 24px;
            height: 24px;
            border: 1.2px solid rgba(99, 102, 239, 1);
            margin: 10px 0 0 13px;
        }

        .title-wrapper {
            flex: 1;
            margin: 10px 0 0 20px;
            text-align: center;
        }

        .main-title {
            color: rgba(0, 0, 0, 1);
            font-size: 16px;
            letter-spacing: 1px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 20px;
            margin-bottom: 10px;
        }

        .header-decoration {
            display: none;
        }

        .decoration-inner {
            display: none;
        }

        .avatar-bg {
            display: none;
        }

        .avatar-container {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 15px;
            width: 60px;
            height: 30px;
            margin: 10px 10px 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .avatar-icon {
            display: none;
        }

        .avatar-img {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        /* 如果图片加载失败，显示默认图标 */
        .avatar-img::before {
            content: "🏃";
            font-size: 14px;
        }

        /* 图片加载成功时隐藏默认内容 */
        .avatar-img[src]:not([src=""]) {
            background: none;
        }

        .avatar-img[src]:not([src=""])::before {
            display: none;
        }

        .avatar-icon-new {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(99, 102, 239, 0.3);
        }

        /* 中间内容区域 */
        .main-content {
            width: 100%;
            max-width: 355px;
            margin: 0 auto;
        }



        /* 聊天气泡区域 */
        .chat-section {
            width: 100%;
            padding: 0 20px 120px 20px;
            margin: 40px 0 20px;
            min-height: 200px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
            scroll-behavior: smooth;
            flex: 1;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .chat-section {
                max-height: calc(100vh - 250px);
                margin: 20px 0 15px;
                padding: 0 15px 120px 15px;
            }
        }

        @media (max-width: 480px) {
            .chat-section {
                max-height: calc(100vh - 200px);
                margin: 15px 0 10px;
                padding: 0 12px 120px 12px;
            }
        }

        /* 确保主容器使用flexbox布局 */
        .main-content {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .chat-message {
            display: flex;
            margin-bottom: 12px;
        }

        .chat-message.user {
            justify-content: flex-end;
        }

        .chat-message.bot {
            justify-content: flex-start;
        }

        .chat-bg {
            display: none;
        }

        .chat-bubble {
            background-color: rgba(95, 88, 255, 1);
            border-radius: 18px 4px 18px 18px;
            padding: 10px 14px;
            max-width: 240px;
            width: fit-content;
            box-shadow: 0 1px 3px rgba(95, 88, 255, 0.3);
        }

        .chat-bubble.bot {
            background-color: rgba(240, 240, 240, 1);
            border-radius: 4px 18px 18px 18px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            max-width: 320px;
            padding: 0;
        }



        .chat-text {
            color: rgba(255, 255, 255, 1);
            font-size: 14px;
            font-family: Microsoft YaHei UI-Regular;
            font-weight: 400;
            line-height: 18px;
            text-align: left;
            letter-spacing: 0.2px;
            word-wrap: break-word;
        }

        .chat-text.bot {
            color: rgba(50, 50, 50, 1);
        }

        /* 功能模块区域 */
        .features {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .feature-btn {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 16px;
            width: calc(50% - 5px);
            height: 40px;
            border: 1px solid rgba(255, 255, 255, 1);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-btn:hover {
            background-color: rgba(255, 255, 255, 0.9);
            transform: translateY(-1px);
        }

        .feature-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-icon {
            width: 16px;
            height: 16px;
        }

        .feature-text {
            color: rgba(23, 20, 51, 1);
            font-size: 12px;
            letter-spacing: 1px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            white-space: nowrap;
            line-height: 12px;
        }

        /* 聊天输入框 - 浮动在底部 */
        .chat-input {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 500px;
            padding: 10px 15px 15px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(240, 240, 240, 0.8);
            z-index: 1000;
            box-sizing: border-box;
        }

        /* 快捷按钮区域 */
        .quick-actions {
            margin-bottom: 10px;
            overflow: hidden;
        }

        .quick-scroll {
            display: flex;
            gap: 8px;
            padding: 0 15px 0 0;
            margin-left: 15px;
            overflow-x: auto;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .quick-scroll::-webkit-scrollbar {
            display: none;
        }

        .quick-btn {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 1);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: rgba(23, 20, 51, 1);
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            white-space: nowrap;
            flex-shrink: 0;
            min-width: fit-content;
        }

        .quick-btn:hover {
            background-color: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .quick-icon {
            width: 14px;
            height: 14px;
            flex-shrink: 0;
        }

        .qa-btn {
            background: linear-gradient(180deg, rgba(228, 233, 253, 0.48) 0%, rgba(255, 255, 255, 0.8) 40.784153%);
            border-radius: 25px;
            width: 100%;
            height: 50px;
            border: 1px solid rgba(255, 255, 255, 1);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            padding: 0 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .qa-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        /* 输入框样式 */
        .chat-input-box {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            width: 100%;
            height: 50px;
            border: 1px solid rgba(95, 88, 255, 1);
            padding: 0 50px 0 15px;
            font-size: 14px;
            outline: none;
            box-shadow: 0 2px 10px rgba(95, 88, 255, 0.2);
        }

        .chat-input-box::placeholder {
            color: rgba(150, 150, 150, 1);
        }

        .send-btn {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 40px;
            height: 40px;
            background: rgba(95, 88, 255, 1);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .send-btn:hover {
            background: rgba(75, 68, 235, 1);
            transform: scale(1.05);
        }

        /* 打字机效果和加载动画 */
        .typing-dots {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: rgba(120, 120, 120, 1);
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 点击反馈动画 */
        .click-feedback {
            transform: scale(0.95);
            transition: transform 0.1s ease;
        }

        /* 禁用状态 */
        .disabled {
            opacity: 0.6;
            pointer-events: none;
        }

        /* 弹出卡片样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-card {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transform: scale(0.8) translateY(20px);
            transition: all 0.3s ease;
        }

        .modal-overlay.show .modal-card {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            padding: 20px 20px 15px;
            border-bottom: 1px solid rgba(240, 240, 240, 1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: rgba(50, 50, 50, 1);
            margin: 0;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: rgba(245, 245, 245, 1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(230, 230, 230, 1);
        }

        .modal-content {
            padding: 20px;
        }

        .modal-section {
            margin-bottom: 20px;
        }

        .modal-section:last-child {
            margin-bottom: 0;
        }

        .modal-section-title {
            font-size: 16px;
            font-weight: 600;
            color: rgba(50, 50, 50, 1);
            margin: 0 0 10px 0;
        }

        .modal-section-desc {
            font-size: 14px;
            color: rgba(120, 120, 120, 1);
            line-height: 1.5;
            margin: 0 0 15px 0;
        }

        .modal-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .modal-option {
            padding: 12px 16px;
            background: rgba(248, 249, 250, 1);
            border: 1px solid rgba(230, 230, 230, 1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: rgba(50, 50, 50, 1);
        }

        .modal-option:hover {
            background: rgba(240, 240, 250, 1);
            border-color: rgba(95, 88, 255, 1);
        }

        .modal-option.selected {
            background: rgba(95, 88, 255, 1);
            color: white;
            border-color: rgba(95, 88, 255, 1);
        }

        .modal-button {
            width: 100%;
            padding: 12px;
            background: rgba(95, 88, 255, 1);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 15px;
        }

        .modal-button:hover {
            background: rgba(75, 68, 235, 1);
        }

        .modal-button:disabled {
            background: rgba(200, 200, 200, 1);
            cursor: not-allowed;
        }

        .send-btn.active {
            display: flex;
        }



        .qa-icon-wrapper {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 25px;
            height: 40px;
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qa-icon {
            width: 20px;
            height: 20px;
        }

        .qa-text {
            color: rgba(184, 191, 208, 1);
            font-size: 14px;
            letter-spacing: 1px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 14px;
            margin-left: 15px;
        }

        /* 介绍区域 */
        .intro {
            background-color: rgba(255, 255, 255, 0.64);
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 1);
            border-radius: 16px;
            padding: 20px;
            box-sizing: border-box;
            margin-bottom: 20px;
        }

        .intro-header {
            margin-bottom: 20px;
            text-align: center;
        }

        .intro-title {
            color: rgba(30, 27, 69, 1);
            font-size: 16px;
            letter-spacing: 0.5px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 20px;
            margin-bottom: 8px;
        }

        .intro-desc {
            color: rgba(132, 138, 173, 1);
            font-size: 12px;
            letter-spacing: 0.5px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            text-align: center;
            line-height: 16px;
        }

        /* 问题列表 */
        .questions {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .question-item {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 24px;
            width: 100%;
            height: 48px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            box-sizing: border-box;
            border: 1px solid rgba(240, 240, 240, 1);
        }

        .question-item:hover {
            background-color: rgba(245, 245, 255, 1);
            transform: translateY(-1px);
        }

        .question-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question-icon {
            width: 14px;
            height: 14px;
        }

        .question-text {
            color: rgba(27, 19, 60, 1);
            font-size: 14px;
            letter-spacing: 0.5px;
            font-family: Microsoft YaHei UI-Regular;
            line-height: 14px;
        }

        .arrow-icon {
            width: 8px;
            height: 12px;
        }

        /* 表单区域 */
        .form {
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 20px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 1);
            padding: 0;
            box-sizing: border-box;
            margin-bottom: 20px;
        }

        .form-header {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 20px 20px 0 0;
            width: 100%;
            padding: 15px 20px;
            border: 1px solid rgba(255, 255, 255, 1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
        }

        .form-title-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .form-title-section .form-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: rgba(50, 50, 50, 1);
            line-height: 1.2;
        }

        .form-title-section .form-desc {
            margin: 0;
            font-size: 12px;
            color: rgba(120, 120, 120, 1);
            line-height: 1.3;
        }

        .form-header-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-icon {
            width: 16px;
            height: 16px;
            background: url(./img/FigmaDDSSlicePNG833c28d0193d1d35e0831b05c9f0b6fa.png) 100% no-repeat;
            background-size: 100% 100%;
        }

        .form-title-wrapper {
            display: flex;
            flex-direction: column;
        }

        .form-title {
            color: rgba(0, 0, 0, 1);
            font-size: 14px;
            letter-spacing: 1px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 16px;
            margin-bottom: 4px;
        }

        .form-subtitle {
            color: rgba(131, 134, 167, 1);
            font-size: 12px;
            letter-spacing: 0.5px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 14px;
        }

        .progress {
            background-color: rgba(239, 236, 253, 1);
            border-radius: 10px;
            width: 38px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1px;
            flex-shrink: 0;
        }

        .progress-text {
            font-size: 11px;
            letter-spacing: 0px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 600;
            line-height: 11px;
        }

        .progress-current {
            color: rgba(93, 89, 210, 1);
            font-size: 11px;
            font-weight: 600;
        }
        .progress-separator {
            color: rgba(171, 179, 198, 1);
            font-size: 10px;
            font-weight: 500;
        }
        .progress-total {
            color: rgba(21, 18, 62, 1);
            font-size: 11px;
            font-weight: 600;
        }

        .question-title {
            color: rgba(0, 0, 0, 1);
            font-size: 16px;
            letter-spacing: 0.5px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 20px;
            margin: 20px 20px 15px;
        }

        /* 选项按钮 */
        .option-btn {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 24px;
            height: 48px;
            width: calc(100% - 40px);
            margin: 8px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .option-btn:hover {
            background-color: rgba(245, 245, 255, 1);
            transform: translateY(-1px);
        }

        .option-btn.selected {
            background-color: rgba(95, 88, 255, 1);
            color: white;
            border-color: rgba(95, 88, 255, 1);
        }

        .option-btn.selected:hover {
            background-color: rgba(75, 68, 235, 1);
        }

        .option-btn.selected {
            background-color: rgba(95, 88, 255, 0.1);
            border: 2px solid rgba(95, 88, 255, 1);
        }

        .option-text {
            color: rgba(24, 19, 51, 1);
            font-size: 14px;
            letter-spacing: 0.5px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 16px;
        }

        .option-btn.selected .option-text {
            color: rgba(95, 88, 255, 1);
        }

        .submit-btn {
            background-color: rgba(91, 86, 238, 1);
            border-radius: 24px;
            height: 48px;
            width: calc(100% - 40px);
            margin: 15px 20px 25px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .submit-btn:hover {
            background-color: rgba(75, 70, 220, 1);
            transform: translateY(-1px);
        }

        .submit-text {
            color: rgba(255, 255, 255, 1);
            font-size: 14px;
            letter-spacing: 1px;
            font-family: Microsoft YaHei UI-Bold;
            font-weight: 700;
            line-height: 16px;
        }

        /* 响应式 */
        @media (max-width: 480px) {
            .page {
                width: 100%;
                max-width: 375px;
                padding: 10px 8px 80px;
            }

            .header,
            .main-content {
                max-width: 100%;
            }

            .chat-input {
                width: 100%;
                padding: 15px 8px;
            }

            .feature-btn {
                width: calc(50% - 5px);
            }
        }
    </style>
</head>
<body>
    <!-- 主页面容器 -->
    <div class="page flex-col">
        <!-- 头部区域 -->
        <header class="header flex-row">
            <div class="header-status flex-col"></div>
            <div class="header-signal flex-col"></div>
            <div class="title-wrapper flex-col">
                <h1 class="main-title">运动健康小助手</h1>
            </div>
            <div class="avatar-container">
                <div class="avatar-icon-new">💪</div>
            </div>
        </header>

        <!-- 中间内容区域 -->
        <div class="main-content">
            <!-- 聊天对话区域 -->
            <section class="chat-section" id="chatSection">
                <!-- 用户消息 -->
                <div class="chat-message user">
                    <div class="chat-bubble">
                        <span class="chat-text">为我生成个性化的训练计划</span>
                    </div>
                </div>

                <!-- AI回复：介绍和问题 -->
                <div class="chat-message bot">
                    <div class="chat-bubble bot">
                        <span class="chat-text bot">你好，我是你的运动健康小助手。不知道如何训练，来问一问，帮你制定专属训练计划，伴你科学运动，避免运动误区</span>
                    </div>
                </div>

                <!-- 介绍和常见问题区域 -->
                <section class="intro">
                    <!-- 介绍文本 -->
                    <div class="intro-header">
                        <h2 class="intro-title">你好，我是你的运动健康小助手</h2>
                        <p class="intro-desc">不知道如何训练，来问一问，帮你制定专属训练计划，伴你科学运动，避免运动误区</p>
                    </div>

                    <!-- 常见问题列表 -->
                    <div class="questions">
                        <div class="question-item">
                            <div class="question-content">
                                <img class="question-icon"
                                     referrerpolicy="no-referrer"
                                     src="./img/FigmaDDSSlicePNG01c5ff2249a1931fb769f5fe308e677a.png"
                                     alt="问题图标">
                                <span class="question-text">减肥的误区有什么？</span>
                            </div>
                            <img class="arrow-icon"
                                 referrerpolicy="no-referrer"
                                 src="./img/FigmaDDSSlicePNGbfc9395ec208fb6e3eadc99506d91c4d.png"
                                 alt="箭头图标">
                        </div>

                        <div class="question-item">
                            <div class="question-content">
                                <img class="question-icon"
                                     referrerpolicy="no-referrer"
                                     src="./img/FigmaDDSSlicePNG01c5ff2249a1931fb769f5fe308e677a.png"
                                     alt="问题图标">
                                <span class="question-text">如何制定科学的运动计划？</span>
                            </div>
                            <img class="arrow-icon"
                                 referrerpolicy="no-referrer"
                                 src="./img/FigmaDDSSlicePNGbfc9395ec208fb6e3eadc99506d91c4d.png"
                                 alt="箭头图标">
                        </div>

                        <div class="question-item">
                            <div class="question-content">
                                <img class="question-icon"
                                     referrerpolicy="no-referrer"
                                     src="./img/FigmaDDSSlicePNG01c5ff2249a1931fb769f5fe308e677a.png"
                                     alt="问题图标">
                                <span class="question-text">运动后如何正确恢复？</span>
                            </div>
                            <img class="arrow-icon"
                                 referrerpolicy="no-referrer"
                                 src="./img/FigmaDDSSlicePNGbfc9395ec208fb6e3eadc99506d91c4d.png"
                                 alt="箭头图标">
                        </div>
                    </div>
                </section>

                <!-- 个人定制训练计划表单区域 -->
        <section class="form flex-col">
            <!-- 表单头部 -->
            <div class="form-header flex-row">
                <div class="form-header-content flex-row justify-between">
                    <div class="form-icon flex-col"></div>
                    <div class="form-title-wrapper flex-col justify-between">
                        <h3 class="form-title">个人定制训练计划</h3>
                        <p class="form-subtitle">请根据你的实际情况进行填写</p>
                    </div>
                </div>
                <!-- 进度指示器 -->
                <div class="progress flex-row align-center">
                    <span class="progress-text progress-current">1</span>
                    <span class="progress-text progress-separator">/</span>
                    <span class="progress-text progress-total">4</span>
                </div>
            </div>

            <!-- 问题标题 -->
            <h4 class="question-title">1.你想要达成的目标是？</h4>

            <!-- 选项按钮 -->
            <button class="option-btn flex-col" type="button" data-value="瘦身减重">
                <span class="option-text">瘦身减重</span>
            </button>

            <button class="option-btn flex-col" type="button" data-value="增肌塑形">
                <span class="option-text">增肌塑形</span>
            </button>

            <button class="option-btn flex-col" type="button" data-value="提高体能">
                <span class="option-text">提高体能</span>
            </button>

            <!-- 确认按钮 -->
            <button class="submit-btn flex-col" type="submit">
                <span class="submit-text">确认</span>
            </button>
                </section>
            </section>
        </div>

    <script>
        // 页面交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 选项按钮点击效果
            const optionButtons = document.querySelectorAll('.option-btn');
            let selectedValue = null;
            let currentStep = 1;
            const totalSteps = 4;

            optionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除其他按钮的选中状态
                    optionButtons.forEach(btn => btn.classList.remove('selected'));
                    // 添加当前按钮的选中状态
                    this.classList.add('selected');
                    selectedValue = this.dataset.value;

                    // 自动进入下一步
                    setTimeout(() => {
                        nextStep();
                    }, 500);
                });
            });

            // 下一步函数
            function nextStep() {
                if (currentStep < totalSteps) {
                    currentStep++;
                    updateFormStep();
                } else {
                    // 完成所有步骤
                    completeForm();
                }
            }

            // 更新表单步骤
            function updateFormStep() {
                const questionTitle = document.querySelector('.question-title');
                const progressCurrent = document.querySelector('.progress-current');

                // 更新进度
                progressCurrent.textContent = currentStep;

                // 移除所有选项的选中状态
                optionButtons.forEach(btn => btn.classList.remove('selected'));

                // 根据步骤更新问题和选项
                switch(currentStep) {
                    case 2:
                        questionTitle.textContent = '2.您的运动经验如何？';
                        updateOptions(['初学者', '有一定基础', '经验丰富']);
                        break;
                    case 3:
                        questionTitle.textContent = '3.您每周可以运动几次？';
                        updateOptions(['1-2次', '3-4次', '5次以上']);
                        break;
                    case 4:
                        questionTitle.textContent = '4.您希望在哪里运动？';
                        updateOptions(['家中', '健身房', '户外']);
                        break;
                }
            }

            // 更新选项按钮文字
            function updateOptions(options) {
                optionButtons.forEach((button, index) => {
                    if (index < options.length) {
                        button.querySelector('.option-text').textContent = options[index];
                        button.dataset.value = options[index];
                        button.style.display = 'flex';
                    } else {
                        button.style.display = 'none';
                    }
                });
            }

            // 完成表单
            function completeForm() {
                const formSection = document.querySelector('.form');
                formSection.innerHTML = `
                    <div style="padding: 40px 20px; text-align: center;">
                        <div style="width: 60px; height: 60px; background: rgba(95, 88, 255, 1); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                            <svg width="30" height="30" viewBox="0 0 24 24" fill="white">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                        </div>
                        <h3 style="color: rgba(0, 0, 0, 1); font-size: 18px; margin-bottom: 10px;">训练计划生成中...</h3>
                        <p style="color: rgba(132, 138, 173, 1); font-size: 14px; margin-bottom: 20px;">正在根据您的信息制定个性化训练方案</p>
                        <div style="width: 100%; height: 4px; background: rgba(240, 240, 240, 1); border-radius: 2px; overflow: hidden;">
                            <div style="width: 0%; height: 100%; background: rgba(95, 88, 255, 1); border-radius: 2px; animation: progress 3s ease-in-out forwards;"></div>
                        </div>
                    </div>
                `;

                // 添加进度条动画
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes progress {
                        to { width: 100%; }
                    }
                `;
                document.head.appendChild(style);

                // 3秒后显示完成信息
                setTimeout(() => {
                    formSection.innerHTML = `
                        <div style="padding: 40px 20px; text-align: center;">
                            <div style="width: 60px; height: 60px; background: rgba(76, 175, 80, 1); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                                <svg width="30" height="30" viewBox="0 0 24 24" fill="white">
                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                </svg>
                            </div>
                            <h3 style="color: rgba(0, 0, 0, 1); font-size: 18px; margin-bottom: 10px;">训练计划已生成！</h3>
                            <p style="color: rgba(132, 138, 173, 1); font-size: 14px; margin-bottom: 20px;">您的个性化训练方案已准备就绪</p>
                            <button style="background: rgba(95, 88, 255, 1); color: white; border: none; border-radius: 24px; padding: 12px 24px; font-size: 14px; cursor: pointer;" onclick="location.reload()">
                                查看训练计划
                            </button>
                        </div>
                    `;
                }, 3000);
            }

            // 聊天状态管理
            let isProcessing = false;
            let currentTypingTimeout = null;

            // 问题项点击效果
            const questionItems = document.querySelectorAll('.question-item');
            const chatSection = document.getElementById('chatSection');

            questionItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 防止重复点击
                    if (isProcessing) {
                        return;
                    }

                    const questionText = this.querySelector('.question-text')?.textContent;
                    if (!questionText) {
                        console.error('无法获取问题文本');
                        return;
                    }

                    console.log('点击了问题:', questionText);

                    // 设置处理状态
                    isProcessing = true;

                    // 添加点击反馈
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);

                    try {
                        // 添加用户问题到聊天区域
                        addChatMessage(questionText, 'user');

                        // 显示AI正在思考
                        showTypingIndicator();

                        // 根据问题类型回复
                        if (questionText.includes('减肥的误区')) {
                            handleWeightLossQuestion();
                        } else if (questionText.includes('运动计划')) {
                            handleExercisePlanQuestion();
                        } else if (questionText.includes('恢复')) {
                            handleRecoveryQuestion();
                        } else {
                            handleGenericQuestion(questionText);
                        }
                    } catch (error) {
                        console.error('处理问题时出错:', error);
                        handleError('抱歉，处理您的问题时出现了错误，请稍后再试。');
                    }
                });
            });

            // 显示AI正在思考的指示器
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'chat-message bot typing-indicator';
                typingDiv.innerHTML = `
                    <div class="chat-bubble bot">
                        <span class="chat-text bot">
                            <span class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                        </span>
                    </div>
                `;

                chatSection.appendChild(typingDiv);
                autoScrollToLatest();

                return typingDiv;
            }

            // 移除思考指示器
            function removeTypingIndicator() {
                const typingIndicator = chatSection.querySelector('.typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            // 添加消息到聊天区域
            function addChatMessage(text, sender, useTypingEffect = false) {
                if (!text || !sender) {
                    console.error('消息文本或发送者类型无效');
                    return;
                }

                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-message ${sender}`;

                const bubbleDiv = document.createElement('div');
                bubbleDiv.className = sender === 'bot' ? 'chat-bubble bot' : 'chat-bubble';

                const textSpan = document.createElement('span');
                textSpan.className = sender === 'bot' ? 'chat-text bot' : 'chat-text';

                bubbleDiv.appendChild(textSpan);
                messageDiv.appendChild(bubbleDiv);
                chatSection.appendChild(messageDiv);

                if (useTypingEffect && sender === 'bot') {
                    // 打字机效果
                    let index = 0;
                    textSpan.textContent = '';

                    function typeChar() {
                        if (index < text.length) {
                            textSpan.textContent += text.charAt(index);
                            index++;
                            currentTypingTimeout = setTimeout(typeChar, 50);
                        }
                    }
                    typeChar();
                } else {
                    textSpan.textContent = text;
                }

                // 自动滚动到最新消息
                autoScrollToLatest();
                return messageDiv;
            }

            // 优化的滚动函数
            function scrollToBottom() {
                setTimeout(() => {
                    const chatSection = document.getElementById('chatSection');
                    if (chatSection) {
                        // 使用平滑滚动
                        chatSection.scrollTo({
                            top: chatSection.scrollHeight,
                            behavior: 'smooth'
                        });
                    }
                }, 100);
            }

            // 自动滚动到最新消息
            function autoScrollToLatest() {
                // 延迟一点时间确保DOM更新完成
                setTimeout(() => {
                    scrollToBottom();
                }, 200);
            }

            // 处理减肥误区问题
            function handleWeightLossQuestion() {
                setTimeout(() => {
                    removeTypingIndicator();

                    const responses = [
                        '节食减肥容易反弹',
                        '只做有氧运动效果有限',
                        '减肥药物存在健康风险',
                        '过度运动可能适得其反',
                        '忽视力量训练是误区',
                        '快速减肥不可持续',
                        '不吃主食会影响代谢',
                        '减肥需要科学方法',
                        '局部减肥是不现实的',
                        '减肥要循序渐进进行'
                    ];

                    // 逐条发送回复，每条间隔800ms
                    responses.forEach((response, index) => {
                        setTimeout(() => {
                            addChatMessage(response, 'bot', true);

                            // 最后一条消息后重置状态
                            if (index === responses.length - 1) {
                                setTimeout(() => {
                                    isProcessing = false;
                                }, 500);
                            }
                        }, index * 800);
                    });
                }, 1500);
            }

            // 处理运动计划问题
            function handleExercisePlanQuestion() {
                setTimeout(() => {
                    removeTypingIndicator();

                    const responses = [
                        '制定运动计划需要考虑个人体质',
                        '建议从低强度运动开始',
                        '每周至少进行3次有氧运动',
                        '力量训练同样重要',
                        '运动前要充分热身',
                        '循序渐进增加运动强度'
                    ];

                    responses.forEach((response, index) => {
                        setTimeout(() => {
                            addChatMessage(response, 'bot', true);
                            if (index === responses.length - 1) {
                                setTimeout(() => { isProcessing = false; }, 500);
                            }
                        }, index * 800);
                    });
                }, 1500);
            }

            // 处理恢复问题
            function handleRecoveryQuestion() {
                setTimeout(() => {
                    removeTypingIndicator();

                    const responses = [
                        '运动后要进行充分的拉伸',
                        '补充足够的水分很重要',
                        '保证充足的睡眠时间',
                        '适当的蛋白质摄入有助恢复',
                        '避免立即进行剧烈活动',
                        '听从身体的信号很关键'
                    ];

                    responses.forEach((response, index) => {
                        setTimeout(() => {
                            addChatMessage(response, 'bot', true);
                            if (index === responses.length - 1) {
                                setTimeout(() => { isProcessing = false; }, 500);
                            }
                        }, index * 800);
                    });
                }, 1500);
            }

            // 处理通用问题
            function handleGenericQuestion(questionText) {
                setTimeout(() => {
                    removeTypingIndicator();
                    addChatMessage('这是一个很好的问题，我来为您详细解答...', 'bot', true);
                    setTimeout(() => {
                        isProcessing = false;
                    }, 1000);
                }, 1500);
            }

            // 错误处理
            function handleError(errorMessage) {
                removeTypingIndicator();
                addChatMessage(errorMessage, 'bot');
                isProcessing = false;
            }

            // 快捷按钮点击效果
            const quickButtons = document.querySelectorAll('.quick-btn');
            quickButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (isProcessing) return;

                    const action = this.dataset.action;
                    const buttonText = this.querySelector('span')?.textContent;
                    console.log('点击了快捷功能:', buttonText);

                    // 添加点击反馈
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);

                    // 根据不同的action执行不同的功能
                    switch(action) {
                        case 'exercise':
                            showExercisePlanCard();
                            break;
                        case 'assessment':
                            handleQuickAction('肩颈评估', '我将为您进行肩颈健康评估，请描述您是否有肩颈不适的症状。');
                            break;
                        case 'nutrition':
                            handleQuickAction('营养建议', '我来为您提供科学的饮食指导，请告诉我您的饮食习惯和健康目标。');
                            break;
                        case 'sleep':
                            handleQuickAction('睡眠管理', '良好的睡眠对健康很重要，请告诉我您的睡眠情况。');
                            break;
                        case 'meditation':
                            handleQuickAction('冥想放松', '冥想可以帮助缓解压力，我来教您一些简单的冥想技巧。');
                            break;
                        case 'record':
                            handleQuickAction('健康记录', '记录健康数据有助于了解身体状况，您想记录哪些健康指标？');
                            break;
                        case 'report':
                            handleQuickAction('健康报告', '我将为您生成健康分析报告，请提供您的基本健康信息。');
                            break;
                        default:
                            handleQuickAction('功能开发中', '该功能正在开发中，敬请期待！');
                    }
                });
            });

            // 显示运动计划卡片
            function showExercisePlanCard() {
                if (isProcessing) return;

                // 添加用户消息
                addChatMessage('运动计划', 'user');

                // 创建运动计划卡片HTML
                const cardHTML = `
                    <section class="form" id="exercisePlanCard">
                        <div class="form-header">
                            <div class="form-title-section">
                                <h3 class="form-title">个性化运动计划</h3>
                                <p class="form-desc">请根据你的实际情况进行填写</p>
                            </div>
                            <div class="progress">
                                <span class="progress-current">1</span>
                                <span class="progress-separator">/</span>
                                <span class="progress-total">3</span>
                            </div>
                        </div>

                        <!-- 问题1：运动目标 -->
                        <h4 class="question-title">1.您的运动目标是什么？</h4>
                        <button class="option-btn flex-col" type="button" data-category="goal" data-value="weight-loss">
                            <span class="option-text">减肥瘦身</span>
                        </button>
                        <button class="option-btn flex-col" type="button" data-category="goal" data-value="muscle-gain">
                            <span class="option-text">增肌塑形</span>
                        </button>
                        <button class="option-btn flex-col" type="button" data-category="goal" data-value="endurance">
                            <span class="option-text">提高体能</span>
                        </button>
                        <button class="option-btn flex-col" type="button" data-category="goal" data-value="health">
                            <span class="option-text">保持健康</span>
                        </button>

                        <!-- 问题2：运动经验 -->
                        <h4 class="question-title" id="question2" style="display: none;">2.您的运动经验如何？</h4>
                        <button class="option-btn flex-col" type="button" data-category="experience" data-value="beginner" style="display: none;">
                            <span class="option-text">初学者（很少运动）</span>
                        </button>
                        <button class="option-btn flex-col" type="button" data-category="experience" data-value="intermediate" style="display: none;">
                            <span class="option-text">有一定基础（偶尔运动）</span>
                        </button>
                        <button class="option-btn flex-col" type="button" data-category="experience" data-value="advanced" style="display: none;">
                            <span class="option-text">经验丰富（经常运动）</span>
                        </button>

                        <!-- 问题3：运动频次 -->
                        <h4 class="question-title" id="question3" style="display: none;">3.每周可运动时间？</h4>
                        <button class="option-btn flex-col" type="button" data-category="time" data-value="1-2" style="display: none;">
                            <span class="option-text">1-2次</span>
                        </button>
                        <button class="option-btn flex-col" type="button" data-category="time" data-value="3-4" style="display: none;">
                            <span class="option-text">3-4次</span>
                        </button>
                        <button class="option-btn flex-col" type="button" data-category="time" data-value="5+" style="display: none;">
                            <span class="option-text">5次以上</span>
                        </button>

                        <!-- 生成按钮 -->
                        <button class="submit-btn flex-col" type="button" id="generatePlanBtn" style="display: none;">
                            <span class="submit-text">生成我的运动计划</span>
                        </button>
                    </section>
                `;

                // 添加卡片到聊天区域
                const cardContainer = document.createElement('div');
                cardContainer.innerHTML = cardHTML;
                chatSection.appendChild(cardContainer);

                // 滚动到新卡片
                autoScrollToLatest();

                // 绑定卡片事件
                bindExerciseCardEvents();
            }

            // 绑定运动计划卡片事件
            function bindExerciseCardEvents() {
                const card = document.getElementById('exercisePlanCard');
                const generateBtn = document.getElementById('generatePlanBtn');
                const progressCurrent = card.querySelector('.progress-current');
                let selectedOptions = {};
                let currentStep = 1;

                // 选项点击事件
                card.querySelectorAll('.option-btn').forEach(option => {
                    option.addEventListener('click', function() {
                        const category = this.dataset.category;
                        const value = this.dataset.value;

                        // 移除同类别其他选项的选中状态
                        card.querySelectorAll(`[data-category="${category}"]`).forEach(opt => {
                            opt.classList.remove('selected');
                        });

                        // 选中当前选项
                        this.classList.add('selected');
                        selectedOptions[category] = value;

                        // 显示下一步
                        if (category === 'goal' && currentStep === 1) {
                            showNextStep(2);
                        } else if (category === 'experience' && currentStep === 2) {
                            showNextStep(3);
                        } else if (category === 'time' && currentStep === 3) {
                            // 显示生成按钮
                            generateBtn.style.display = 'flex';
                            autoScrollToLatest();
                        }
                    });
                });

                // 显示下一步问题
                function showNextStep(step) {
                    currentStep = step;
                    progressCurrent.textContent = step;

                    if (step === 2) {
                        // 显示运动经验问题
                        document.getElementById('question2').style.display = 'block';
                        card.querySelectorAll('[data-category="experience"]').forEach(btn => {
                            btn.style.display = 'flex';
                        });
                    } else if (step === 3) {
                        // 显示运动频次问题
                        document.getElementById('question3').style.display = 'block';
                        card.querySelectorAll('[data-category="time"]').forEach(btn => {
                            btn.style.display = 'flex';
                        });
                    }

                    autoScrollToLatest();
                }

                // 生成计划按钮
                generateBtn.addEventListener('click', function() {
                    generateExercisePlan(selectedOptions);
                    // 隐藏卡片
                    card.style.display = 'none';
                });
            }

            // 生成运动计划
            function generateExercisePlan(options) {
                // 显示AI思考
                isProcessing = true;
                showTypingIndicator();

                setTimeout(() => {
                    removeTypingIndicator();

                    // 根据选择生成个性化计划
                    const plan = generatePersonalizedPlan(options);

                    // 逐条显示计划
                    plan.forEach((item, index) => {
                        setTimeout(() => {
                            addChatMessage(item, 'bot', true);

                            if (index === plan.length - 1) {
                                setTimeout(() => {
                                    isProcessing = false;
                                }, 500);
                            }
                        }, index * 800);
                    });
                }, 1500);
            }

            // 生成个性化运动计划
            function generatePersonalizedPlan(options) {
                const { goal, experience, time } = options;

                let plan = ['根据您的选择，为您制定以下运动计划：'];

                // 根据目标添加建议
                switch(goal) {
                    case 'weight-loss':
                        plan.push('🎯 目标：减肥瘦身');
                        plan.push('💪 建议：有氧运动为主，配合力量训练');
                        plan.push('🏃‍♀️ 推荐：跑步、游泳、骑行');
                        break;
                    case 'muscle-gain':
                        plan.push('🎯 目标：增肌塑形');
                        plan.push('💪 建议：力量训练为主，适量有氧');
                        plan.push('🏋️‍♀️ 推荐：器械训练、自重训练');
                        break;
                    case 'endurance':
                        plan.push('🎯 目标：提高体能');
                        plan.push('💪 建议：有氧和无氧结合训练');
                        plan.push('🏃‍♀️ 推荐：间歇训练、功能性训练');
                        break;
                    case 'health':
                        plan.push('🎯 目标：保持健康');
                        plan.push('💪 建议：适度运动，注重持续性');
                        plan.push('🚶‍♀️ 推荐：快走、瑜伽、太极');
                        break;
                }

                // 根据经验调整强度
                switch(experience) {
                    case 'beginner':
                        plan.push('📈 强度：从低强度开始，循序渐进');
                        break;
                    case 'intermediate':
                        plan.push('📈 强度：中等强度，稳步提升');
                        break;
                    case 'advanced':
                        plan.push('📈 强度：可进行高强度训练');
                        break;
                }

                // 根据时间安排频次
                switch(time) {
                    case '1-2':
                        plan.push('⏰ 频次：每周1-2次，每次45-60分钟');
                        break;
                    case '3-4':
                        plan.push('⏰ 频次：每周3-4次，每次30-45分钟');
                        break;
                    case '5+':
                        plan.push('⏰ 频次：每周5次以上，每次30分钟');
                        break;
                }

                plan.push('✨ 记住：坚持比强度更重要！');

                return plan;
            }

            // 处理快捷功能
            function handleQuickAction(title, message) {
                if (isProcessing) return;

                isProcessing = true;

                addChatMessage(title, 'user');

                showTypingIndicator();

                setTimeout(() => {
                    removeTypingIndicator();
                    addChatMessage(message, 'bot', true);
                    setTimeout(() => {
                        isProcessing = false;
                    }, 1000);
                }, 1500);
            }



            // 提交按钮点击
            const submitButton = document.querySelector('.submit-btn');
            if (submitButton) {
                submitButton.addEventListener('click', function() {
                    if (selectedValue) {
                        console.log('选择的目标:', selectedValue);
                        alert('您选择的目标是: ' + selectedValue);
                        // 这里可以添加提交表单的逻辑
                    } else {
                        alert('请先选择一个目标');
                    }
                });
            }
            // 底部输入框功能
            const qaBtn = document.getElementById('qaBtn');
            const chatInputBox = document.getElementById('chatInputBox');
            const sendBtn = document.getElementById('sendBtn');
            let isInputMode = false;

            // 问答按钮点击 - 切换到输入模式
            if (qaBtn) {
                qaBtn.addEventListener('click', function() {
                    if (isProcessing) return;

                    if (!isInputMode) {
                        // 切换到输入模式
                        qaBtn.style.display = 'none';
                        chatInputBox.style.display = 'block';
                        sendBtn.classList.add('active');
                        chatInputBox.focus();
                        isInputMode = true;
                    }
                });
            }

            // 发送消息功能
            function sendMessage() {
                if (isProcessing) return;

                const message = chatInputBox?.value?.trim();

                if (!message) {
                    return;
                }

                // 清空输入框
                chatInputBox.value = '';

                // 添加用户消息
                addChatMessage(message, 'user');

                // 显示AI思考
                isProcessing = true;
                showTypingIndicator();

                // 模拟AI回复
                setTimeout(() => {
                    removeTypingIndicator();

                    // 根据消息内容生成回复
                    let response = generateResponse(message);
                    addChatMessage(response, 'bot', true);

                    setTimeout(() => {
                        isProcessing = false;
                    }, 1000);
                }, 1500);

                // 切换回问答模式
                chatInputBox.style.display = 'none';
                qaBtn.style.display = 'flex';
                sendBtn.classList.remove('active');
                isInputMode = false;
            }

            // 生成AI回复
            function generateResponse(message) {
                const lowerMessage = message.toLowerCase();

                if (lowerMessage.includes('减肥') || lowerMessage.includes('瘦身')) {
                    return '减肥需要科学的方法，建议结合合理饮食和适量运动，避免极端节食。';
                } else if (lowerMessage.includes('运动') || lowerMessage.includes('锻炼')) {
                    return '运动是保持健康的重要方式，建议根据个人体质选择合适的运动项目。';
                } else if (lowerMessage.includes('饮食') || lowerMessage.includes('营养')) {
                    return '均衡的营养摄入很重要，建议多吃蔬菜水果，适量摄入蛋白质。';
                } else if (lowerMessage.includes('睡眠')) {
                    return '良好的睡眠对健康至关重要，建议保持规律的作息时间。';
                } else {
                    return '感谢您的提问，我会尽力为您提供专业的健康建议。';
                }
            }

            // 发送按钮点击事件
            if (sendBtn) {
                sendBtn.addEventListener('click', sendMessage);
            }

            // 输入框事件处理
            if (chatInputBox) {
                // 回车发送
                chatInputBox.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        sendMessage();
                    }
                });

                // 失焦处理
                chatInputBox.addEventListener('blur', function() {
                    setTimeout(() => {
                        if (!this.value.trim() && !isProcessing && isInputMode) {
                            // 如果输入框为空且没有在处理，切换回问答模式
                            chatInputBox.style.display = 'none';
                            qaBtn.style.display = 'flex';
                            sendBtn.classList.remove('active');
                            isInputMode = false;
                        }
                    }, 200);
                });
            }
        });
    </script>

    <!-- 底部浮动聊天区域 -->
    <div class="chat-input">
        <!-- 快捷按钮 -->
        <div class="quick-actions">
            <div class="quick-scroll">
                <div class="quick-btn" data-action="exercise">
                    <div class="quick-icon" style="background: url(./img/FigmaDDSSlicePNGf76d962093d42f4ab4c3493838d649f2.png) 100% no-repeat; background-size: 100% 100%;"></div>
                    <span>运动计划</span>
                </div>
                <div class="quick-btn" data-action="assessment">
                    <div class="quick-icon" style="background: url(./img/FigmaDDSSlicePNG1e4c1a512319fe44d3451303ea980558.png) 100% no-repeat; background-size: 100% 100%;"></div>
                    <span>肩颈评估</span>
                </div>
                <div class="quick-btn" data-action="nutrition">
                    <div class="quick-icon" style="background: url(./img/FigmaDDSSlicePNG352d2dbd74ecda97c6cf7a8b7c952549.png) 100% no-repeat; background-size: 100% 100%;"></div>
                    <span>营养建议</span>
                </div>
                <div class="quick-btn" data-action="sleep">
                    <div class="quick-icon" style="background: url(./img/FigmaDDSSlicePNG01c5ff2249a1931fb769f5fe308e677a.png) 100% no-repeat; background-size: 100% 100%;"></div>
                    <span>睡眠管理</span>
                </div>
                <div class="quick-btn" data-action="meditation">
                    <div class="quick-icon" style="background: url(./img/FigmaDDSSlicePNGf76d962093d42f4ab4c3493838d649f2.png) 100% no-repeat; background-size: 100% 100%;"></div>
                    <span>冥想放松</span>
                </div>
                <div class="quick-btn" data-action="record">
                    <div class="quick-icon" style="background: url(./img/FigmaDDSSlicePNG1e4c1a512319fe44d3451303ea980558.png) 100% no-repeat; background-size: 100% 100%;"></div>
                    <span>健康记录</span>
                </div>
                <div class="quick-btn" data-action="report">
                    <div class="quick-icon" style="background: url(./img/FigmaDDSSlicePNG352d2dbd74ecda97c6cf7a8b7c952549.png) 100% no-repeat; background-size: 100% 100%;"></div>
                    <span>健康报告</span>
                </div>
            </div>
        </div>

        <!-- 聊天消息容器 -->
        <div class="chat-messages" id="chatMessages" style="display: none;"></div>

        <!-- 聊天输入区域 -->
        <div class="chat-input-container" style="position: relative;">
            <!-- 默认显示的问答按钮 -->
            <div class="qa-btn" id="qaBtn">
                <div class="qa-icon-wrapper">
                    <img class="qa-icon"
                         referrerpolicy="no-referrer"
                         src="./img/FigmaDDSSlicePNG352d2dbd74ecda97c6cf7a8b7c952549.png"
                         alt="健康问答图标">
                </div>
                <span class="qa-text">健康问题尽管问我</span>
            </div>

            <!-- 输入框（初始隐藏） -->
            <input type="text" class="chat-input-box" id="chatInputBox" placeholder="请输入您的健康问题..." maxlength="200">
            <button class="send-btn" id="sendBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
        </div>
    </div>
</body>
</html>

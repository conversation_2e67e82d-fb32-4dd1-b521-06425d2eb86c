#!/bin/bash

# 抽奖记录导出功能优化提交脚本

echo "开始提交抽奖记录导出功能优化..."

# 检查当前目录
echo "当前工作目录: $(pwd)"

# 添加修改的文件
git add shangchengquan/shangcheng/app/controller/Choujiang.php
git add 抽奖记录导出功能优化说明.md

# 提交更改
git commit -m "feat: 优化抽奖记录导出功能，增加详细表单信息和物流信息

功能优化:
- 在导出Excel中新增详细表单信息列，完整显示用户填写的所有表单数据
- 新增物流相关信息列：快递公司、快递单号、发货时间、物流状态
- 将联系人和手机号分离为独立列，提高数据可读性
- 优化表单数据解析，支持JSON格式的formdata字段解析
- 增加物流状态中文显示：未发货/已发货/已收货
- 添加导出操作日志记录

导出字段优化:
- 原有字段：序号、活动ID、活动名称、会员ID、昵称、奖品、兑奖信息、领取时间、状态、备注
- 新增字段：联系人、手机号、详细表单信息、快递公司、快递单号、发货时间、物流状态

技术实现:
- 解析formdata JSON字段，格式化为可读的表单信息
- 兼容旧版本数据，对空字段进行安全处理
- 优化时间格式显示，统一为 Y-m-d H:i:s 格式
- 增加异常处理，确保导出功能稳定性

使用场景:
- 客服人员查看完整的用户信息和表单填写内容
- 物流管理人员跟踪发货状态和快递信息
- 数据分析和历史记录备份
- 用户信息统计和客户服务支持

修改文件:
- shangchengquan/shangcheng/app/controller/Choujiang.php: 优化recordexcel方法
- 抽奖记录导出功能优化说明.md: 详细功能说明文档"

echo "提交完成！"

# 显示提交状态
git status

echo "抽奖记录导出功能优化已成功提交到Git仓库"

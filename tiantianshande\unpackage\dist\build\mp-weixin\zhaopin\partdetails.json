{"navigationBarTitleText": "", "enablePullDownRefresh": false, "usingComponents": {"job-list": "/zhaopin/components/jobList/index", "apply-sure-modal": "/zhaopin/components/applySureModal/index", "apply-button": "/zhaopin/components/applyButton/applyButton", "education": "/zhaopin/templates/education/education", "report-dialog": "/zhaopin/components/reportDialog/index", "simple-model": "/zhaopin/components/simpleModel/simpleModel", "navigation-bar": "/zhaopin/components/navigationBar/index", "regular-item": "/zhaopin/components/regularItem/index"}}
<template>
  <view class="intro">
    <!-- 介绍文本 -->
    <view class="intro-header">
      <text class="intro-title">你好，我是你的运动健康小助手</text>
      <text class="intro-desc">不知道如何训练，来问一问，帮你制定专属训练计划，伴你科学运动，避免运动误区</text>
    </view>

    <!-- 常见问题列表 -->
    <view class="questions">
      <view 
        v-for="(question, index) in questionList" 
        :key="index"
        class="question-item"
        @tap="handleQuestionClick(question)"
      >
        <view class="question-content">
          <image
            class="question-icon"
            :src="pre_url + '/static/img/FigmaDDSSlicePNG01c5ff2249a1931fb769f5fe308e677a.png'"
            mode="aspectFit"
          />
          <text class="question-text">{{ question.text }}</text>
        </view>
        <image
          class="arrow-icon"
          :src="pre_url + '/static/img/FigmaDDSSlicePNGbfc9395ec208fb6e3eadc99506d91c4d.png'"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  name: 'IntroComponent',
  data() {
    return {
      pre_url: app.globalData.pre_url,
      questionList: [
        {
          id: 1,
          text: '减肥的误区有什么？',
          category: 'weight-loss'
        },
        {
          id: 2,
          text: '如何制定科学的运动计划？',
          category: 'exercise-plan'
        },
        {
          id: 3,
          text: '运动后如何正确恢复？',
          category: 'recovery'
        }
      ]
    }
  },
  methods: {
    // 处理问题点击
    handleQuestionClick(question) {
      console.log('2025-01-26 10:00:00,001-INFO-[IntroComponent][handleQuestionClick_001] 问题点击:', question);
      
      // 添加点击反馈效果
      const clickedElement = event.currentTarget;
      clickedElement.style.transform = 'scale(0.98)';
      setTimeout(() => {
        clickedElement.style.transform = '';
      }, 150);
      
      // 向父组件发送问题点击事件
      this.$emit('question-click', question.text);
    }
  }
}
</script>

<style scoped>
/* 介绍区域 - 参考HTML现代化设计 */
.intro {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  width: 100%;
  border: 1rpx solid rgba(99, 102, 239, 0.15);
  border-radius: 32rpx;
  padding: 56rpx;
  box-sizing: border-box;
  margin-bottom: 40rpx;
  backdrop-filter: blur(25rpx);
  box-shadow: 0 16rpx 64rpx rgba(99, 102, 239, 0.12), 0 6rpx 20rpx rgba(99, 102, 239, 0.08);
  position: relative;
  overflow: hidden;
}

/* 介绍卡片发光效果 */
.intro::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 239, 0.4), transparent);
}

/* 介绍卡片装饰性渐变 */
.intro::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(99, 102, 239, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

/* 介绍头部 */
.intro-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.intro-title {
  color: rgba(20, 20, 30, 0.95);
  font-size: 40rpx;
  letter-spacing: -0.3rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 700;
  line-height: 52rpx;
  margin-bottom: 24rpx;
  display: block;
  text-align: center;
  position: relative;
  z-index: 2;
  text-shadow: 0 2rpx 4rpx rgba(99, 102, 239, 0.08);
}

/* 标题装饰效果 */
.intro-title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, rgba(99, 102, 239, 0.6), rgba(139, 92, 246, 0.6));
  border-radius: 2rpx;
}

.intro-desc {
  color: rgba(80, 80, 100, 0.85);
  font-size: 28rpx;
  letter-spacing: 0.3rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 500;
  text-align: center;
  line-height: 42rpx;
  display: block;
  position: relative;
  z-index: 2;
}

/* 问题列表 */
.questions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.question-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 24rpx;
  width: 100%;
  height: 112rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 36rpx;
  box-sizing: border-box;
  border: 1rpx solid rgba(99, 102, 239, 0.1);
  box-shadow: 0 6rpx 24rpx rgba(99, 102, 239, 0.06), 0 2rpx 8rpx rgba(99, 102, 239, 0.04);
  backdrop-filter: blur(15rpx);
  position: relative;
  overflow: hidden;
}

/* 问题项发光边框 */
.question-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 239, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.question-item:hover {
  background: linear-gradient(135deg, rgba(240, 242, 247, 0.98) 0%, rgba(235, 238, 245, 0.95) 100%);
  transform: translateY(-6rpx);
  box-shadow: 0 12rpx 48rpx rgba(99, 102, 239, 0.15), 0 4rpx 16rpx rgba(99, 102, 239, 0.1);
  border-color: rgba(99, 102, 239, 0.3);
}

.question-item:hover::before {
  opacity: 1;
}

.question-item:active {
  transform: translateY(-3rpx) scale(0.98);
}

/* 问题项光效动画 */
.question-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 239, 0.05), transparent);
  transition: left 0.6s ease;
}

.question-item:hover::after {
  left: 100%;
}

/* 问题内容 */
.question-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.question-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}

.question-text {
  color: rgba(20, 20, 30, 0.9);
  font-size: 32rpx;
  letter-spacing: 0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 600;
  line-height: 40rpx;
  flex: 1;
  position: relative;
  z-index: 2;
}

/* 箭头图标 - 参考HTML现代化设计 */
.arrow-icon {
  width: 20rpx;
  height: 28rpx;
  flex-shrink: 0;
  opacity: 0.7;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2rpx 4rpx rgba(99, 102, 239, 0.1));
  position: relative;
  z-index: 2;
}

.question-item:hover .arrow-icon {
  opacity: 1;
  transform: translateX(6rpx) scale(1.1);
  filter: drop-shadow(0 4rpx 8rpx rgba(99, 102, 239, 0.2));
}

/* 响应式调整 */
@media (max-width: 768px) {
  .intro {
    padding: 48rpx;
    margin-bottom: 36rpx;
    border-radius: 28rpx;
  }

  .intro-title {
    font-size: 36rpx;
    line-height: 48rpx;
    margin-bottom: 20rpx;
  }

  .intro-desc {
    font-size: 26rpx;
    line-height: 38rpx;
  }

  .question-item {
    height: 104rpx;
    padding: 0 32rpx;
    border-radius: 20rpx;
  }

  .question-text {
    font-size: 28rpx;
    line-height: 36rpx;
  }

  .question-icon {
    width: 26rpx;
    height: 26rpx;
  }

  .arrow-icon {
    width: 18rpx;
    height: 26rpx;
  }
}

@media (max-width: 480px) {
  .intro {
    padding: 40rpx;
    margin-bottom: 32rpx;
    border-radius: 24rpx;
  }

  .intro-header {
    margin-bottom: 36rpx;
  }

  .intro-title {
    font-size: 32rpx;
    line-height: 44rpx;
    margin-bottom: 18rpx;
  }

  .intro-desc {
    font-size: 24rpx;
    line-height: 36rpx;
  }
  
  .questions {
    gap: 12rpx;
  }
  
  .question-item {
    height: 96rpx;
    padding: 0 28rpx;
    border-radius: 18rpx;
  }

  .question-content {
    gap: 20rpx;
  }

  .question-text {
    font-size: 26rpx;
    line-height: 34rpx;
  }

  .question-icon {
    width: 24rpx;
    height: 24rpx;
  }

  .arrow-icon {
    width: 16rpx;
    height: 20rpx;
  }
}
</style>

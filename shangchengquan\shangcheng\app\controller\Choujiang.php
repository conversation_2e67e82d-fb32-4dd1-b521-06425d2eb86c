<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 抽奖活动
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\View;
use think\facade\Db;

class Choujiang extends Common
{
    public function initialize(){
		parent::initialize();
		if(bid > 0) showmsg('无访问权限');
	}
	//活动列表
	public function index(){
		if(request()->isAjax()){
			$page = input('param.page');
			$limit = input('param.limit');
			if(input('param.field') && input('param.order')){
				$order = input('param.field').' '.input('param.order');
			}else{
				$order = 'id desc';
			}
			$where = array();
			$where[] = ['aid','=',aid];
			if(input('param.name')) $where[] = ['name','like','%'.input('param.name').'%'];
			$count = 0 + Db::name('choujiang')->where($where)->count();
			$data = Db::name('choujiang')->where($where)->page($page,$limit)->order($order)->select()->toArray();

			return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
		}
		return View::fetch();
	}
	//编辑
	public function edit(){
		if(input('param.id')){
			$info = Db::name('choujiang')->where('aid',aid)->where('id',input('param.id/d'))->find();
		}else{
			$info = array(
				'id'=>'',
				'guize'=>'1.活动时间：'.date('Y年m月d日').'——'.date('Y年m月d日',time()+86400).'。
2.活动期间，每人每天可参与抽奖3次。
3.本次活动奖品为奖品一、奖品二、奖品三。
4.实物奖品请到指定地点领取；红包奖品将直接发放微信红包，请注意领取；优惠券将直接发放到您的个人账户请注意查收。',
				'pertotal'=>'3',
				'perday'=>'3',
				'shareaddnum'=>'0',
				'sharedaytimes'=>'0',
				'sharetimes'=>0,
				'starttime'=>time()-100,
				'endtime'=>time()+86400-100,
				'use_type'=>1,
				'usescore'=>0,
				'usemoney'=>0,
				'usecontribution'=>0,
				'status'=>1,
				'sharepic'=>'',
				'gettj'=>'-1',
				'j0mc'=>'谢谢参与',
				'j0pic'=>PRE_URL.'/static/img/dzp/face.png',
				'j0sl'=>'55',
				'j0yj'=>'0',
				'j1mc'=>'奖品一',
				'j1pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j1sl'=>'5',
				'j1yj'=>'0',
				'j2mc'=>'奖品二',
				'j2pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j2sl'=>'10',
				'j2yj'=>'0',
				'j3mc'=>'奖品三',
				'j3pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j4pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j5pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j6pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j7pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j8pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j9pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j10pic'=>PRE_URL.'/static/img/dzp/jiangpin.png',
				'j3sl'=>'30',
				'j3yj'=>'0',
				'formcontent'=>'[{"key":"input","val1":"姓名","val2":"","val3":"1"},{"key":"input","val1":"手机号","val2":"","val3":"1"}]',

			);
			if(input('param.type')=='dzp'){
				$info['type'] = 'dzp';
				$info['name'] = '幸运大转盘活动开始啦';
				$info['banner'] = PRE_URL.'/static/img/dzp/title.png';
				$info['bgpic'] = PRE_URL.'/static/img/dzp/bg.png';
                $info['bgcolor']='#F58D40';
			}
			if(input('param.type')=='ggk'){
				$info['type'] = 'ggk';
				$info['name'] = '刮刮卡活动开始啦';
				$info['banner'] = PRE_URL.'/static/img/ggk/title.png';
                $info['bgcolor']='#C40004';
			}
		}
		$info['gettj'] = explode(',',$info['gettj']);
		View::assign('info',$info);

		$default_cid = Db::name('member_level_category')->where('aid',aid)->where('isdefault', 1)->value('id');
        $default_cid = $default_cid ? $default_cid : 0;
        $memberlevel = Db::name('member_level')->where('aid',aid)->where('cid', $default_cid)->order('sort,id')->select()->toArray();
		View::assign('memberlevel',$memberlevel);
		return View::fetch();
	}
	//保存
	public function save(){
		$info = input('post.info/a');
		$info['starttime'] = strtotime($info['starttime']);
		$info['endtime'] = strtotime($info['endtime']);
		$info['gettj'] = implode(',',$info['gettj']);
		$datatype = input('post.datatype/a');
		$dataval1 = input('post.dataval1/a');
		$dataval2 = input('post.dataval2/a');
		$dataval3 = input('post.dataval3/a');
		$dhdata = array();
		foreach($datatype as $k=>$v){
			if($dataval3[$k]!=1) $dataval3[$k] = 0;
			$dhdata[] = array('key'=>$v,'val1'=>$dataval1[$k],'val2'=>$dataval2[$k],'val3'=>$dataval3[$k]);
		}
		$info['formcontent'] = jsonEncode($dhdata);

		if($info['id']){
			$info['updatetime'] = time();
			Db::name('choujiang')->where('aid',aid)->where('id',$info['id'])->update($info);
			\app\common\System::plog('编辑抽奖活动'.$info['id']);
		}else{
			$info['aid'] = aid;
			$info['createtime'] = time();
			$id = Db::name('choujiang')->insertGetId($info);
			\app\common\System::plog('添加抽奖活动'.$id);
		}
		return json(['status'=>1,'msg'=>'操作成功','url'=>(string)url('index')]);
	}
	//删除
	public function del(){
		$ids = input('post.ids/a');
		Db::name('choujiang')->where('aid',aid)->where('id','in',$ids)->delete();
		\app\common\System::plog('删除抽奖活动'.implode(',',$ids));
		return json(['status'=>1,'msg'=>'删除成功']);
	}
	//领取记录
	public function record(){
		if(request()->isAjax()){
			$page = input('param.page');
			$limit = input('param.limit');
			if(input('param.field') && input('param.order')){
				$order = input('param.field').' '.input('param.order');
			}else{
				$order = 'id desc';
			}
			$where = [];
			$where[] = ['aid','=',aid];
			if(input('param.hid')){
				$where[] = ['hid','=',input('param.hid/d')];
			}
			if(input('param.mid')) $where[] = ['mid','=',input('param.mid')];
			if(input('param.nickname')) $where[] = ['nickname','like','%'.input('param.nickname').'%'];
			if(input('param.linkman')) $where[] = ['formdata','like','%'.input('param.linkman').'%'];
			if(input('param.jxmc')) $where[] = ['jxmc','like','%'.input('param.jxmc').'%'];
			if(input('param.ctime') ){
				$ctime = explode(' ~ ',input('param.ctime'));
				$where[] = ['createtime','>=',strtotime($ctime[0])];
				$where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
			}
			if(input('?param.status') && input('param.status')!==''){
				$where[] = ['status','=',input('param.status')];
				if(input('param.status') == 0){
					$where[] = ['jx','<>',0];
				}
			}
			$count = 0 + Db::name('choujiang_record')->where($where)->count();
			$data = Db::name('choujiang_record')->where($where)->page($page,$limit)->order($order)->select()->toArray();
			foreach($data as $k=>$v){
				$formdataArr = array();
				$formdata = json_decode($v['formdata'],true);
				foreach($formdata as $k2=>$v2){
					$formdataArr[] = $k2.'：'.$v2;
				}
				$data[$k]['formdata'] = implode('<br>',$formdataArr);
				// 设置默认物流状态
				if(!isset($data[$k]['express_status'])) $data[$k]['express_status'] = 0;
			}
			return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
		}
		View::assign('express_data',express_data());
		return View::fetch();
	}
	//改状态
	public function setst(){
		$ids = input('post.ids/a');
		$st = input('post.st/d');
		Db::name('choujiang_record')->where('aid',aid)->where('id','in',$ids)->update(['status'=>$st]);
		\app\common\System::plog('修改抽奖记录状态'.implode(',',$ids));
		return json(['status'=>1,'msg'=>'修改成功']);
	}

	//发货
	public function sendExpress(){
		$recordid = input('post.recordid/d');
		$express_com = input('post.express_com');
		$express_no = input('post.express_no');

		if(!$recordid || !$express_com || !$express_no){
			return json(['status'=>0,'msg'=>'参数错误']);
		}

		$record = Db::name('choujiang_record')->where('aid',aid)->where('id',$recordid)->find();
		if(!$record){
			return json(['status'=>0,'msg'=>'记录不存在']);
		}

		if($record['jx'] == 0){
			return json(['status'=>0,'msg'=>'未中奖记录无法发货']);
		}

		// 更新发货信息
		$updateData = [
			'express_com' => $express_com,
			'express_no' => $express_no,
			'send_time' => time(),
			'express_status' => 1
		];

		Db::name('choujiang_record')->where('aid',aid)->where('id',$recordid)->update($updateData);

		// 发送发货通知
		if($record['mid']){
			$tmplcontent = [];
			$tmplcontent['first'] = '您的抽奖奖品已发货';
			$tmplcontent['remark'] = '请点击查看详情~';
			$tmplcontent['keyword1'] = $record['jxmc'];
			$tmplcontent['keyword2'] = $express_com;
			$tmplcontent['keyword3'] = $express_no;
			$tmplcontent['keyword4'] = $record['linkman'].' '.$record['tel'];

			$tmplcontentNew = [];
			$tmplcontentNew['thing4'] = $record['jxmc'];//奖品名称
			$tmplcontentNew['thing13'] = $express_com;//快递公司
			$tmplcontentNew['character_string14'] = $express_no;//快递单号
			$tmplcontentNew['thing16'] = $record['linkman'].' '.$record['tel'];//收货人

			\app\common\Wechat::sendtmpl(aid,$record['mid'],'tmpl_orderfahuo',$tmplcontent,m_url('pages/my/usercenter'),$tmplcontentNew);
		}

		\app\common\System::plog('抽奖奖品发货'.$recordid);
		return json(['status'=>1,'msg'=>'发货成功']);
	}

	//查物流
	public function getExpress(){
		$recordid = input('post.recordid/d');
		$record = Db::name('choujiang_record')->where('aid',aid)->where('id',$recordid)->find();

		if(!$record || !$record['express_no']){
			return json(['status'=>0,'msg'=>'暂无物流信息']);
		}

		$expressData = \app\common\Common::getwuliu($record['express_no'],$record['express_com']);

		return json(['status'=>1,'data'=>$expressData,'record'=>$record]);
	}
	//领取记录导出
	public function recordexcel(){
		$where = [];
		$where[] = ['aid','=',aid];
		if(input('param.hid')){
			$where[] = ['hid','=',input('param.hid/d')];
		}
		$list = Db::name('choujiang_record')->where($where)->select()->toArray();

		$title = array();
		$title[] = '序号';
		$title[] = '活动ID';
		$title[] = '活动名称';
		$title[] = t('会员').'ID';
		$title[] = '昵称';
		$title[] = '奖品';
		$title[] = '联系人';
		$title[] = '手机号';
		$title[] = '详细表单信息';
		$title[] = '快递公司';
		$title[] = '快递单号';
		$title[] = '发货时间';
		$title[] = '物流状态';
		$title[] = '领取时间';
		$title[] = '状态';
		$title[] = '备注';
		$data = array();
		foreach($list as $v){
			$tdata = array();
			$tdata[] = $v['id'];
			$tdata[] = $v['hid'];
			$tdata[] = $v['name'];
			$tdata[] = $v['mid'];
			$tdata[] = $v['nickname'];
			$tdata[] = $v['jxmc'];
			$tdata[] = $v['linkman'] ?: '';
			$tdata[] = $v['tel'] ?: '';

			// 解析详细表单信息
			$formdataStr = '';
			if($v['formdata']){
				$formdata = json_decode($v['formdata'], true);
				if($formdata && is_array($formdata)){
					$formdataArr = array();
					foreach($formdata as $k2=>$v2){
						$formdataArr[] = $k2.'：'.$v2;
					}
					$formdataStr = implode("\r\n", $formdataArr);
				}
			}
			$tdata[] = $formdataStr;

			// 物流信息
			$tdata[] = isset($v['express_com']) ? $v['express_com'] : '';
			$tdata[] = isset($v['express_no']) ? $v['express_no'] : '';
			$tdata[] = isset($v['send_time']) && $v['send_time'] ? date('Y-m-d H:i:s', $v['send_time']) : '';

			// 物流状态
			$expressStatus = '';
			if(isset($v['express_status'])){
				switch($v['express_status']){
					case 0: $expressStatus = '未发货'; break;
					case 1: $expressStatus = '已发货'; break;
					case 2: $expressStatus = '已收货'; break;
					default: $expressStatus = '未发货'; break;
				}
			}
			$tdata[] = $expressStatus;

			$tdata[] = date('Y-m-d H:i:s',$v['createtime']);
			$status = '';
			if($v['jx']==0){
				$status = '未中奖';
			}elseif($v['status']==1){
				$status = '已领取';
			}elseif($v['status']==0){
				$status = '未领取';
			}
			$tdata[] = $status;
			$tdata[] = $v['remark'] ?: '';
			$data[] = $tdata;
		}
		\app\common\System::plog('导出抽奖记录，共'.count($data).'条记录');
		$this->export_excel($title,$data);
	}
	//删除
	public function recorddel(){
		$ids = input('post.ids/a');
		Db::name('choujiang_record')->where('aid',aid)->where('id','in',$ids)->delete();
		\app\common\System::plog('删除抽奖记录'.implode(',',$ids));
		return json(['status'=>1,'msg'=>'删除成功']);
	}
}
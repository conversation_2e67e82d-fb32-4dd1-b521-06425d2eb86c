# 物流数据统一使用说明

## 概述

系统中所有涉及物流发货的功能都统一使用相同的数据源和接口，确保用户体验一致性和数据管理的统一性。

## 核心数据源

### 1. 快递公司数据函数

**位置：** `shangchengquan/shangcheng/app/common.php` 第749行

```php
function express_data(){
    return array(
        '申通快递'=>'STO',
        'EMS'=>'EMS', 
        '顺丰速运'=>'SFEXPRESS',
        '圆通速递'=>'YTO',
        '中通快递'=>'ZTO',
        '韵达快递'=>'YUNDA',
        '天天快递'=>'TTKDEX',
        '百世快递'=>'HTKY',
        '全峰快递'=>'QFKD',
        '德邦快递'=>'DEPPON',
        '宅急送'=>'ZJS',
        // ... 更多快递公司
        '极兔快递'=>'JITU',
        '京东'=>'JD',
        '自提'=>'',
        '其他'=>''
    );
}
```

**数据格式说明：**
- Key: 快递公司名称（用于显示和存储）
- Value: 快递公司代码（用于物流查询API）

### 2. 物流查询接口

**统一调用：** `\app\common\Common::getwuliu($express_no, $express_com)`

**参数说明：**
- `$express_no`: 快递单号
- `$express_com`: 快递公司名称（使用express_data中的key）

## 系统中的使用情况

### 1. 订单系统

**控制器：** `ShopOrder.php`
```php
// 传递快递数据到视图
View::assign('express_data', express_data());
```

**视图模板：** `shop_order/index.html`
```html
<!-- 发货弹窗中的快递公司选择 -->
{foreach $express_data as $k=>$v}
<option value="{$k}">{$k}</option>
{/foreach}
```

**物流查询：**
```php
$list = \app\common\Common::getwuliu($order['express_no'], $order['express_com']);
```

### 2. 抽奖系统

**控制器：** `Choujiang.php`
```php
// 传递快递数据到视图
View::assign('express_data', express_data());
```

**视图模板：** `choujiang/record.html`
```html
<!-- 发货弹窗中的快递公司选择 -->
{foreach $express_data as $k=>$v}
html+='<option value="{$k}">{$k}</option>';
{/foreach}
```

**物流查询：**
```php
$expressData = \app\common\Common::getwuliu($record['express_no'], $record['express_com']);
```

### 3. 其他订单系统

以下系统都使用相同的模式：
- **团购订单** (`TuangouOrder.php`)
- **砍价订单** (`KanjiaOrder.php`) 
- **秒杀订单** (`SeckillOrder.php`)
- **积分商城订单** (`ScoreshopOrder.php`)
- **周期购订单** (`ZujiOrder.php`)

## 统一标准

### 1. 控制器标准

```php
// 在需要显示发货界面的方法中
View::assign('express_data', express_data());
```

### 2. 视图模板标准

```html
<!-- 快递公司选择下拉框 -->
<select name="express_com">
    <option value="">请选择快递公司</option>
    {foreach $express_data as $k=>$v}
    <option value="{$k}">{$k}</option>
    {/foreach}
</select>
```

### 3. 发货处理标准

```php
// 获取快递信息
$express_com = input('post.express_com');  // 快递公司名称
$express_no = input('post.express_no');    // 快递单号

// 存储到数据库（存储快递公司名称，不是代码）
$updateData = [
    'express_com' => $express_com,
    'express_no' => $express_no,
    'send_time' => time(),
    'express_status' => 1
];
```

### 4. 物流查询标准

```php
// 查询物流信息
$expressData = \app\common\Common::getwuliu($record['express_no'], $record['express_com']);
```

## 数据一致性保证

### 1. 存储格式
- 数据库中存储快递公司的**名称**（如："顺丰速运"）
- 不存储快递公司代码（如："SFEXPRESS"）

### 2. 显示格式
- 用户界面显示快递公司**名称**
- 下拉选择框的value和显示文本都使用快递公司名称

### 3. API调用格式
- 物流查询API使用快递公司名称作为参数
- `Common::getwuliu()` 方法内部会处理名称到代码的转换

## 维护说明

### 1. 添加新快递公司

只需在 `express_data()` 函数中添加：
```php
'新快递公司名称' => '快递公司代码'
```

### 2. 修改快递公司信息

直接在 `express_data()` 函数中修改对应项即可，所有使用该函数的模块会自动更新。

### 3. 删除快递公司

从 `express_data()` 函数中移除对应项，但需要考虑历史数据的兼容性。

## 优势

### 1. 统一管理
- 所有快递公司信息集中在一个函数中管理
- 修改一处，全系统生效

### 2. 用户体验一致
- 所有发货界面的快递公司选择保持一致
- 物流查询功能在各个模块中表现一致

### 3. 数据规范
- 统一的数据格式和存储标准
- 避免不同模块间的数据不一致问题

### 4. 易于维护
- 新增或修改快递公司只需修改一个地方
- 代码复用性高，减少重复开发

## 注意事项

### 1. 兼容性
- 修改快递公司信息时要考虑历史数据
- 删除快递公司前要检查是否有相关订单使用

### 2. 物流API
- 确保 `Common::getwuliu()` 方法支持所有配置的快递公司
- 新增快递公司时要测试物流查询功能

### 3. 数据验证
- 发货时要验证快递公司是否在支持列表中
- 防止用户提交无效的快递公司信息

## 相关文件

- **核心函数**: `shangchengquan/shangcheng/app/common.php`
- **抽奖系统**: `shangchengquan/shangcheng/app/controller/Choujiang.php`
- **订单系统**: `shangchengquan/shangcheng/app/controller/ShopOrder.php`
- **物流查询**: `shangchengquan/shangcheng/app/common/Common.php`

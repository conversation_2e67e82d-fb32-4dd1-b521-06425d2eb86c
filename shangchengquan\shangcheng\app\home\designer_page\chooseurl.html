<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>选择链接</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
	<link href="__STATIC__/imgsrc/designer.css" rel="stylesheet">
	<style>
	#urlqr img{margin:0 auto}
	.fe-tab-link::-webkit-scrollbar {width: 2px;height: 0px;}
	.fe-tab-link::-webkit-scrollbar-thumb {border-radius: 1px;-webkit-box-shadow: inset 0 0 1px rgba(0,0,0,0.5);background: rgba(0,0,0,0.5);}
	.fe-tab-link::-webkit-scrollbar-track {-webkit-box-shadow: inset 0 0 0px rgba(0,0,0,0.2);border-radius: 0;background: rgba(0,0,0,0);}
	</style>
</head>
<body style="margin:0;">
<div class="layui-fluid">
<div class="layui-row layui-col-space15">
	<div class="layui-card layui-col-md12">
	<div class="layui-tab">
		<ul class="layui-tab-title">
				<li id="fe-tab-link-nav-1" style="padding:0 8px" class="layui-this">基础功能</li>
				{if $auth_data=='all' || in_array('ShopCategory/*',$auth_data)}<li id="fe-tab-link-nav-2" style="padding:0 8px">商品分类</li>{/if}
				{if $auth_data=='all' || in_array('ShopGroup/*',$auth_data)}<li id="fe-tab-link-nav-3" style="padding:0 8px">商品分组</li>{/if}
				{if $auth_data=='all' || in_array('ShopProduct/*',$auth_data)}<li id="fe-tab-link-nav-4" style="padding:0 8px">商城商品</li>{/if}
				{if $auth_data=='all' || in_array('CollageProduct/*',$auth_data)}<li id="fe-tab-link-nav-5" style="padding:0 8px">拼团商品</li>{/if}
				{if $auth_data=='all' || in_array('KanjiaProduct/*',$auth_data)}<li id="fe-tab-link-nav-6" style="padding:0 8px">砍价商品</li>{/if}
				{if $auth_data=='all' || in_array('ScoreshopProduct/*',$auth_data)}<li id="fe-tab-link-nav-7" style="padding:0 8px">{:t('积分')}商城</li>{/if}
				{if $auth_data=='all' || in_array('ArticleCategory/*',$auth_data)}<li id="fe-tab-link-nav-8" style="padding:0 8px">文章分类</li>{/if}
				{if $auth_data=='all' || in_array('Article/*',$auth_data)}<li id="fe-tab-link-nav-9" style="padding:0 8px">文章</li>{/if}
				{if $auth_data=='all' || in_array('Business/category',$auth_data)}<li id="fe-tab-link-nav-10" style="padding:0 8px">商家分类</li>{/if}
				{if $auth_data=='all' || in_array('Business/*',$auth_data)}<li id="fe-tab-link-nav-11" style="padding:0 8px">商家</li>{/if}
				{if getcustom('xixie')}
					{if $auth_data=='all' || in_array('XixieCategory/*',$auth_data)}<li id="fe-tab-link-2" style="padding:0 8px">洗鞋商品分类</li>{/if}
				{/if}
				{if $auth_data=='all' || in_array('DesignerPage/*',$auth_data) || in_array('DesignerPage/index',$auth_data)}<li id="fe-tab-link-12" style="padding:0 8px">设计页面</li>{/if}
				{if $Request.param.type!='geturl'}<li id="fe-tab-link-13" style="padding:0 8px">功能</li>{/if}

				{if $auth_data=='all' || in_array('CycleProduct/*',$auth_data)}<li id="fe-tab-link-14" style="padding:0 8px">周期购商品</li>{/if}
				{if getcustom('huodong_baoming')}
					{if $auth_data=='all' || in_array('HuodongBaomingCategory/*',$auth_data)}<li id="fe-tab-link-nav-15" style="padding:0 8px">活动分类</li>{/if}
					{if $auth_data=='all' || in_array('HuodongBaomingList/*',$auth_data)}<li id="fe-tab-link-nav-16" style="padding:0 8px">活动列表</li>{/if}
				{/if}
				{if getcustom('hotel')}
					{if $auth_data=='all' || in_array('HotelCategory/*',$auth_data)}<li id="fe-tab-link-nav-17" style="padding:0 8px">{$text['酒店']}分类</li>{/if}
					{if $auth_data=='all' || in_array('Hotel/*',$auth_data)}<li id="fe-tab-link-nav-18" style="padding:0 8px">{$text['酒店']}列表</li>{/if}
				{/if}

		</ul>
		<div class="layui-tab-content" style="padding:0">
				<div class="layui-tab-item layui-show fe-tab-link" id="fe-tab-link-1">
						<div class="page-header" style="margin:20px 0 0 0;padding-bottom:0">
								<h4><i class="fa fa-folder-open-o"></i> 基础页面</h4>
						</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pages/index/index','首页')">首页</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pages/my/usercenter','{:t('会员')}中心')">{:t('会员')}中心</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/classify','分类商品')">分类商品</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/prolist','商品列表')">商品列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/shopPackage/shop/cart','购物车')">购物车</div>
						{if $auth_data=='all' || in_array('ShopProduct/*',$auth_data)}
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
								<h4><i class="fa fa-folder-open-o"></i> 商城页面</h4>
						</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/category1','一级分类')">一级分类</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/category2','二级分类')">二级分类</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/category4','二级分类2')">二级分类2</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/category3','三级分类')">三级分类</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/classify','分类商品')">分类商品</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/classify2','分类商品2')">分类商品2</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/prolist','商品列表')">商品列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/fastbuy','快速购买')">快速购买</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/shop/fastbuy2','快速购买2')">快速购买2</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/shopPackage/shop/cart','购物车')">购物车</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/shopPackage/shop/search','商品搜索')">商品搜索</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/order/orderlist','订单列表')">订单列表</div>
						{if getcustom('pay_daifu')}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pages/pay/pay','收银台')">收银台</div>
						{/if}

						{/if}
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
								<h4><i class="fa fa-folder-open-o"></i> 活动页面</h4>
						</div>
						{if $auth_data=='all' || in_array('SeckillSet/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/activity/seckill/index','秒杀列表')">秒杀列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/seckill/orderlist','秒杀订单')">秒杀订单</div>
						{/if}

						{if $auth_data=='all' || in_array('KanjiaProduct/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/kanjia/index','砍价列表')">砍价首页</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/kanjia/orderlist','砍价订单')">砍价订单</div>
						{/if}
						{if $auth_data=='all' || in_array('CollageProduct/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/collage/index','拼团列表')">拼团首页</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/collage/orderlist','拼团订单')">拼团订单</div>
						{/if}
						{if $auth_data=='all' || in_array('ScoreshopProduct/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/scoreshop/index','{:t('积分')}商城')">积分商城</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/scoreshop/orderlist','{:t('积分')}商城订单')">积分商城订单</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/scoreshop/rank','{:t('积分')}排行榜')">积分排行榜</div>
						{/if}
						{if getcustom('plug_luckycollage')}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/luckycollage/index','幸运拼团预告页')">幸运拼团预告页</div>
						{/if}
						{if $auth_data=='all' || in_array('LuckyCollageProduct/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/luckycollage/classify','幸运拼团分类')">幸运拼团分类</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/luckycollage/prolist','幸运拼团列表')">幸运拼团列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/luckycollage/orderlist','幸运拼团订单')">幸运拼团订单</div>
						{/if}

						{if $auth_data=='all' || in_array('TuangouProduct/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/tuangou/prolist','团购商品')">团购商品</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/tuangou/orderlist','团购订单')">团购订单</div>
						{/if}

						{if $auth_data=='all' || in_array('Sign/set',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/sign/index','签到')">签到</div>
						{/if}
						{if $auth_data=='all' || in_array('Backstage/setfenhongdian',$auth_data)}	
	<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/my/duihuanzhanghao','兑换账号（七星）')">兑换账号（七星）</div>
	
		{/if}
		{if $auth_data=='all' || in_array('ShenqingbandianSet/set',$auth_data)}
		<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/highVoltage/highVoltageApplication','申请办电')">申请办电</div>
			{/if}
		{if $auth_data=='all' || in_array('Paidui/sysset',$auth_data)}
			<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/paidui/paidui','排队免单')">排队免单</div>
		{/if}
				
					{if $auth_data=='all' || in_array('ExamTiku/index',$auth_data)}
			<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/kecheng/list','题库列表')">题库列表</div>
			<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/kecheng/orderlist','题库订单')">题库订单</div>
		
		{/if}
				{if $auth_data=='all' || in_array('daxue/index',$auth_data)}
			<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/daxuepage/blist','大学列表')">大学列表</div>
		
		
		{/if}
		
			
			<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/zhuanzhang/zhuanjifen','{:t('积分')}转账')">{:t('积分')}转账</div>
			
	 	  <div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/zhuanzhang/zhuanzhang','{:t('余额')}转账')">{:t('余额')}转账</div>
	 	  
	 	  <div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/zhuanzhang/heizhuanzhang','{:t('现金券')}转账')">{:t('现金券')}转账</div>
	 	  
	 	  <div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/zhuanzhang/zhuanzhanggxz','{:t('贡献值')}转账')">{:t('贡献值')}转账</div>
	 	  
	 	  <div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/zhuanzhang/zhuanzhuanghjf','{:t('黄积分')}转账')">{:t('黄积分')}转账</div>
			
			
				{if $auth_data=='all' || in_array('Daihuoyiuan/index',$auth_data)}
			<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/daihuobiji/kuaituan/tuanlist','带货团列表')">带货团列表</div>	
			
			<!--<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/daihuobiji/kuaituan/tuanlist','带货团列表')">带货团列表</div>	-->
			
				<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/tuanzhang/apply','团长入驻')">团长入驻</div>	
			
			<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/tuanzhangadmin/index/login','团长登陆')">团长登陆</div>
			{/if}
				{if $auth_data=='all' || in_array('Daihuobiji',$auth_data)}
				<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/daihuobiji/detail/fatie','带货团列表')">发笔记</div>	
					<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/daihuobiji/detail/userbiji','笔记个人中心')">笔记个人中心</div>	
				{/if}
				
					{if $auth_data=='all' || in_array('Renzhengset',$auth_data)}
				<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'pagesExa/my/renzhengzhongxin','认证中心')">认证中心</div>	
				{/if}
						{if getcustom('everyday_hongbao') && ($auth_data=='all' || in_array('HongbaoEveryday/*',$auth_data))}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/hongbaoEveryday/index','每日红包')">每日红包</div>
						{/if}
						{if getcustom('yx_kouling') && ($auth_data=='all' || in_array('Kouling/*',$auth_data))}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/yx/kouling','口令')">口令</div>
						{/if}

						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
								<h4><i class="fa fa-folder-open-o"></i> {:t('会员')}中心</h4>
						</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pages/my/usercenter','{:t('会员')}中心')">{:t('会员')}中心</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/my/favorite','我的收藏')">我的收藏</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/my/history','我的足迹')">我的足迹</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/money/recharge','{:t('会员')}充值')">{:t('会员')}充值</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/money/moneylog','{:t('余额')}明细')">{:t('余额')}明细</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/money/withdraw','{:t('余额')}提现')">{:t('余额')}提现</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/my/set','个人设置')">个人设置</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/my/paypwd','支付密码')">支付密码</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/my/levelinfo','{:t('会员')}等级')">{:t('会员')}等级</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExa/my/levelup','{:t('会员')}升级')">{:t('会员')}升级</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pages/address/address','收货地址')">收货地址</div>
						{if $auth_data=='all' || in_array('Coupon/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/coupon/couponlist','领券中心')">领券中心</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/coupon/mycoupon','我的{:t('优惠券')}')">我的{:t('优惠券')}</div>
						{/if}
						{if getcustom('woniudz')}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/myteamline','我的上下级')">我的上下级</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/mysameline','我的同级会员')">我的同级会员</div>
						{/if}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesB/login/login','用户登录')">用户登录</div>
						{if getcustom('other_money')}
							{if $othermoney_status}
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/moneylog?type=money2','{:t('余额2')}明细')">{:t('余额2')}明细</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/withdraw?type=money2','{:t('余额2')}提现')">{:t('余额2')}提现</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/moneylog?type=money3','{:t('余额3')}明细')">{:t('余额3')}明细</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/withdraw?type=money3','{:t('余额3')}提现')">{:t('余额3')}提现</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/moneylog?type=money4','{:t('余额4')}明细')">{:t('余额4')}明细</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/withdraw?type=money4','{:t('余额4')}提现')">{:t('余额4')}提现</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/moneylog?type=money5','{:t('tivity/yuyue/apply余额5')}明细')">{:t('余额5')}明细</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/withdraw?type=money5','{:t('余额5')}提现')">{:t('余额5')}提现</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/othermoney/frozen_moneylog','{:t('冻结金额')}明细')">{:t('冻结金额')}明细</div>
							{/if}
						{/if}
						{if getcustom('member_archives')}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/archives/index','档案列表')">档案列表</div>
						{/if}
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
								<h4><i class="fa fa-folder-open-o"></i> 分销页面</h4>
						</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/activity/commission/myteam','我的团队')">我的团队</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/index','我的{:t('佣金')}')">我的{:t('佣金')}</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/downorder','分销订单')">分销订单</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/commissionlog','{:t('佣金')}明细')">{:t('佣金')}明细</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/poster','分享海报')">分享海报</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/fhlog','分红记录')">分红记录</div>
						{if getcustom('commissionranking')}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/commission/commissionranking','佣金排行榜')">佣金排行榜</div>
						{/if}

						{if $auth_data=='all' || in_array('Restaurant/*',$auth_data)}
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
								<h4><i class="fa fa-folder-open-o"></i> 餐饮页面</h4>
						</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/takeaway/index?bid={$bid}','外卖')">外卖</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/takeaway/orderlist','外卖订单')">外卖订单</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/shop/index?bid={$bid}','点餐')">点餐</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/shop/orderlist','点餐订单')">点餐订单</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/booking/add?bid={$bid}','预定')">预定</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/booking/orderlist','预定记录')">预定记录</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/queue/index?bid={$bid}','排队')">排队</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/queue/record?bid={$bid}','排队记录')">排队记录</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/restaurant/deposit/orderdetail','寄存')">寄存</div>
						{/if}

						{if getcustom('zhaopin') && $bid==0 && ($auth_data=='all' || in_array('Zhaopin/*',$auth_data))}
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
							<h4><i class="fa fa-folder-open-o"></i> 招聘页面</h4>
						</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/zhaopin/index','招聘')">招聘列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/qiuzhi/index','求职')">求职列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/zhaopin/add','发布招聘')">发布招聘</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/qiuzhi/add','发布求职')">发布求职</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/zhaopin/record','我的投递')">我的投递</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/zhaopin/my','我的发布')">我的发布</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/zhaopin/assuranceorder','保证金')">保证金</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/notice/index','消息中心')">消息中心</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/qiuzhi/qianyue','技师签约')">技师签约</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/zhaopin/agent/apply','代理入驻')">代理入驻</div>
						{/if}
						{if getcustom('hotel')}
						{if $auth_data=='all' || in_array('Hotel/*',$auth_data)}
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
							<h4><i class="fa fa-folder-open-o"></i> {$text['酒店']}页面</h4>
						</div>
						<div class="layui-btn-div">
							<div class="layui-btn layui-btn-primary layui-btn-sm" onclick="chooseLink(1,'/hotel/index/index','{$text['酒店']}首页')">{$text['酒店']}首页</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" onclick="chooseLink(1,'/hotel/index/hotellist','{$text['酒店']}首页')">{$text['酒店']}列表</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" onclick="chooseLink(1,'/hotel/order/orderlist','{$text['酒店']}订单')">{$text['酒店']}订单</div>
						</div>
						{/if}
					{/if}
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">
								<h4><i class="fa fa-folder-open-o"></i> 其他页面</h4>
						</div>
						{if $auth_data=='all' || in_array('Business/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pagesExt/business/apply','申请入驻')">申请入驻</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/business/blist','商家列表')">商家列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/business/clist','分类商家')">分类商家</div>
						{/if}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/maidan/pay','买单付款')">买单付款</div>
					
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/maidan/maidanlog','买单记录')">买单记录</div>
						{if $auth_data=='all' || in_array('Form/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExb/form/formlog','提交记录')">表单提交记录</div>
						{/if}
						{if $auth_data=='all' || in_array('Article/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/article/artlist','文章列表')">文章列表</div>
						{/if}
						{if $auth_data=='all' || in_array('Lipin/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/lipin/index','礼品卡兑换')">礼品卡兑换</div>
						{/if}
						{if $auth_data=='all' || in_array('Luntan/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/luntan/index','用户论坛')">用户论坛</div>
							{if getcustom('luntan_call')}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/luntan/fatielog','论坛发帖记录')">论坛发帖记录</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/luntan/focuslog','论坛关注记录')">论坛关注记录</div>
							{/if}
						{/if}

						{if $auth_data=='all' || in_array('Yuyue/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/yuyue/prolist?bid={$bid}','预约服务')">预约服务</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/yuyue/orderlist?bid={$bid}','预约订单')">预约订单</div>
							{if getcustom('hmy_yuyue')}
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/yuyue/peolist2?bid={$bid}','师傅列表')">师傅列表</div>
							{/if}
							{if getcustom('yuyue_apply')}
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/yuyue/apply?bid={$bid}','预约师傅申请')">预约师傅申请</div>
							{/if}
						{/if}

						{if $auth_data=='all' || in_array('KechengList/*',$auth_data)}
						{if getcustom('kecheng_discount')}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/kecheng/category3','课程分类')">课程分类</div>
						{/if}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/kecheng/list?bid={$bid}','课程列表')">课程列表</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/kecheng/orderlist?bid={$bid}','我的课程')">我的课程</div>
						{/if}
						{if $auth_data=='all' || in_array('Shortvideo/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/activity/shortvideo/index','短视频')">短视频</div>
						{/if}
						
						{if getcustom('express')}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/activity/express/index','查寄快递')">查寄快递</div>
						{/if}
						{if getcustom('workorder')}
							{if $auth_data=='all' || in_array('WorkorderCategory/*',$auth_data)}
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/activity/workorder/index','提交工单')">提交工单</div>
								<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/activity/workorder/record','工单记录')">工单记录</div>
							{/if}
						{/if}
						{if getcustom('plug_yuebao')}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/activity/yuebao/withdraw','余额宝')">余额宝</div>
						{/if}
						{if $image_search}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/shop/imgsearch?bid={$bid}','图搜')">图搜</div>
						{/if}
						{if $diy_light}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/shop/diylight?bid={$bid}','配灯')">配灯</div>
						{/if}
						{if getcustom('pay_yuanbao')}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pagesExt/yuanbao/yuanbaolog','{:t('元宝')}')">{:t('元宝')}</div>
						{/if}
						{if getcustom('renovation_calculator') && ($auth_data=='all' || in_array('RenovationCalculator/*',$auth_data))}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pagesExt/renovation/form','装修计算器')">装修计算器</div>
						{/if}
						{if getcustom('counsel_fee') && ($auth_data=='all' || in_array('CounselFee/*',$auth_data))}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pagesExt/counsel/index','律师费计算器')">律师费计算器</div>
						{/if}
						{if getcustom('legal_fee') && ($auth_data=='all' || in_array('LegalFee/*',$auth_data))}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pagesExt/legal/index','诉讼费计算器')">诉讼费计算器</div>
						{/if}
						{if getcustom('invite_free')}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/invite_free/index','我的免单')">我的免单</div>
						{/if}
						{if getcustom('xixie')}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/index','洗鞋商品列表')">洗鞋商品列表</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/orderlist','洗鞋订单列表')">洗鞋订单列表</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/mendianorder','洗鞋门店订单列表')">洗鞋门店订单列表</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/mendianwithdraw','洗鞋提现')">洗鞋提现</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/mendianmoneylog','洗鞋余额记录')">洗鞋余额记录</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/membervip','洗鞋开通会员')">洗鞋开通会员</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/pslogin','洗鞋取货员登录页')">洗鞋取货员登录页</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/psdating','洗鞋取货员接单大厅')">洗鞋取货员接单大厅</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/xixie/psorderlist','洗鞋取货员订单列表')">洗鞋取货员订单列表</div>
						{/if}
						{if getcustom('yueke')}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/yueke/prolist','约课列表')">约课列表</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/yueke/orderlist','约课记录')">约课记录</div>
						{/if}
						





						
						{if $auth_data=='all' || in_array('Mingpian/*',$auth_data)}
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/mingpian/index','名片详情')">名片详情</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/mingpian/edit','创建名片')">创建名片</div>
							<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/mingpian/favorite','名片夹')">名片夹</div>
						{/if}
						
						{if $auth_data=='all' || in_array('CycleProduct/*',$auth_data)}
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/cycle/prolist','周期购商品')">周期购商品</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-top:10px" onclick="chooseLink(1,'/pagesExt/cycle/orderList','周期购订单')">周期购订单</div>
						{/if}
						
						<div class="page-header" style="margin:10px 0 0 0;padding-bottom:0">

						</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/admin/index/index','手机端后台')">手机端后台</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/activity/peisong/dating','配送员抢单')">配送员抢单</div>
						<div class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left:10px;margin-top:10px" onclick="chooseLink(1,'/pagesExt/kefu/index','在线咨询')">在线咨询</div>

				</div>
				{if $auth_data=='all' || in_array('ShopCategory/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-2">
					{foreach $clist as $c}
					<div>
						<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLinkCategory(2,'{$c.id}','{$c.name}')">选择</a></div>
							<div style="float:left;">
							{if $c['pic']}<img src="{$c.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
						</div>
						<div class="fe-tab-link-text" style="float:left">
							{$c.name}</div>
						</div>
					</div>
					{foreach $c['child'] as $v}
					<div style="width:95%;float:right">
						<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(2,'/pagesExb/shop/prolist?cid={$v.id}','{$v.name}')">选择</a></div>
							<div style="float:left;">
							{if $v['pic']}<img src="{$v.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
						</div>
						<div class="fe-tab-link-text" style="float:left">
							{$v.name}</div>
						</div>
					</div>
					{foreach $v['child'] as $v2}
					<div style="width:90%;float:right">
						<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(2,'/pagesExb/shop/prolist?cid={$v2.id}','{$v2.name}')">选择</a></div>
							<div style="float:left;">
							{if $v2['pic']}<img src="{$v2.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
						</div>
						<div class="fe-tab-link-text" style="float:left">
							{$v2.name}</div>
						</div>
					</div>
					{/foreach}
					{/foreach}
					{/foreach}
				</div>
				{/if}
				{if $auth_data=='all' || in_array('ShopGroup/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-3">
					{foreach $glist as $g}
					<div>
						<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(3,'/pagesExb/shop/prolist?gid={$g.id}','{$g.name}')">选择</a></div>
							<div style="float:left;">
							{if $g['pic']}<img src="{$g.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
						</div>
						<div class="fe-tab-link-text" style="float:left">
							{$g.name}</div>
						</div>
					</div>
					{/foreach}
				</div>
				{/if}
				{if $auth_data=='all' || in_array('ShopProduct/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-4">
						<div class="layui-form layui-inline" style="margin-top:10px">
							<div class="layui-input-inline">
									<input type="text" class="layui-input" style="height:30px;" name="productname" value="" placeholder=请输入商品名称进行搜索>
							</div>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-product">搜索</button>
						</div>
						<div class="layui-col-md12" style="margin-top:5px">
							<table id="producttabledata" lay-filter="producttabledata"></table>
						</div>
				</div>
				{/if}
				{if $auth_data=='all' || in_array('CollageProduct/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-5">
						<div class="layui-form layui-inline" style="margin-top:10px">
							<div class="layui-input-inline">
									<input type="text" class="layui-input" style="height:30px;" name="collageproductname" value="" placeholder=请输入商品名称进行搜索>
							</div>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-collageproduct">搜索</button>
						</div>
						<div class="layui-col-md12" style="margin-top:5px">
							<table id="collageproducttabledata" lay-filter="collageproducttabledata"></table>
						</div>
				</div>
				{/if}
				{if $auth_data=='all' || in_array('KanjiaProduct/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-6">
						<div class="layui-form layui-inline" style="margin-top:10px">
							<div class="layui-input-inline">
									<input type="text" class="layui-input" style="height:30px;" name="kanjiaproductname" value="" placeholder=请输入商品名称进行搜索>
							</div>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-kanjiaproduct">搜索</button>
						</div>
						<div class="layui-col-md12" style="margin-top:5px">
							<table id="kanjiaproducttabledata" lay-filter="kanjiaproducttabledata"></table>
						</div>
				</div>
				{/if}
				{if $auth_data=='all' || in_array('ScoreshopProduct/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-7">
						<div class="layui-form layui-inline" style="margin-top:10px">
							<div class="layui-input-inline">
									<input type="text" class="layui-input" style="height:30px;" name="scoreshopproductname" value="" placeholder=请输入商品名称进行搜索>
							</div>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-scoreshopproduct">搜索</button>
						</div>
						<div class="layui-col-md12" style="margin-top:5px">
							<table id="scoreshopproducttabledata" lay-filter="scoreshopproducttabledata"></table>
						</div>
				</div>
				{/if}
				{if $auth_data=='all' || in_array('ArticleCategory/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-8">
					{foreach $aclist as $c}
					<div>
						<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
							{if getcustom('article_portion')}
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(8,'/pagesExt/article/artlist?cid={$c.id}&look_type=1','{$c.name}')">选择</a></div>
							{/if}
							{if !getcustom('article_portion')}
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(8,'/pagesExt/article/artlist?cid={$c.id}','{$c.name}')">选择</a></div>
							{/if}
							<div style="float:left;">
							{if $c['pic']}<img src="{$c.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
						</div>
						<div class="fe-tab-link-text" style="float:left">
							{$c.name}</div>
						</div>
					</div>
					{foreach $c['child'] as $v}
					<div style="width:95%;float:right">
						<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
							{if getcustom('article_portion')}
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(8,'/pagesExt/article/artlist?cid={$v.id}&look_type=1','{$v.name}')">选择</a></div>
							{/if}
							{if !getcustom('article_portion')}
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(8,'/pagesExt/article/artlist?cid={$v.id}','{$v.name}')">选择</a></div>
							{/if}
							<div style="float:left;">
							{if $v['pic']}<img src="{$v.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
						</div>
						<div class="fe-tab-link-text" style="float:left">
							{$v.name}</div>
						</div>
					</div>
					{/foreach}
					{/foreach}
				</div>
				{/if}
				{if $auth_data=='all' || in_array('Article/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-9">
						<div class="layui-form layui-inline" style="margin-top:10px">
							<div class="layui-input-inline">
									<input type="text" class="layui-input" style="height:30px;" name="articlename" value="" placeholder=请输入文章标题进行搜索>
							</div>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-article">搜索</button>
						</div>
						<div class="layui-col-md12" style="margin-top:5px">
							<table id="articletabledata" lay-filter="articletabledata"></table>
						</div>
				</div>
				{/if}
				{if $auth_data=='all' || in_array('Business/category',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-10">
					{foreach $bclist as $bc}
					<div>
						<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(10,'/pagesExt/business/blist?cid={$bc.id}','{$bc.name}')">商家列表</a></div>
							<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(10,'/pagesExt/business/clist?cid={$bc.id}','{$bc.name}')">分类商家</a></div>
							<div style="float:left;">
							{if $bc['pic']}<img src="{$bc.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
						</div>
						<div class="fe-tab-link-text" style="float:left">
							{$bc.name}</div>
						</div>
					</div>
					{/foreach}
				</div>
				{/if}

				{if $auth_data=='all' || in_array('Business/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-11">
						<div class="layui-form layui-inline" style="margin-top:10px">
							<div class="layui-input-inline">
									<input type="text" class="layui-input" style="height:30px;" name="businessname" value="" placeholder=请输入商家名称进行搜索>
							</div>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-business">搜索</button>
						</div>
						<div class="layui-col-md12" style="margin-top:5px">
							<table id="businesstabledata" lay-filter="businesstabledata"></table>
						</div>
				</div>
				{/if}


				

                        {if getcustom('huodong_baoming')}
                            <!-- 活动分类标签页 -->
                            {if $bid==0 && ($auth_data=='all' || in_array('HuodongBaomingCategory/*',$auth_data))}
                                <div class="layui-tab-item fe-tab-link" id="fe-tab-link-15">
                                    {foreach $hdbaomingcatelist as $c}
                                        <div>
                                            <div class="fe-tab-link-line">
                                                <div class="fe-tab-link-sub">
                                                    <a href="javascript:void(0);" onclick="chooseLink(3,'/pagesB/huodongbaoming/prolist2?cid={$c.id}','{$c.name}')">选择</a>
                                                </div>
                                                <div style="float:left;">
                                                    {if $c['pic']}<img src="{$c.pic}" class="category-img"/>{/if}
                                                </div>
                                                <div class="fe-tab-link-text">
                                                    {$c.name}
                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>
                            {/if}

                            <!-- 活动列表标签页 -->
                            {if $auth_data=='all' || in_array('HuodongBaomingList/*',$auth_data)}
                                <div class="layui-tab-item fe-tab-link" id="fe-tab-link-16">
                                    <div class="layui-form layui-inline search-form">
                                        <div class="layui-input-inline">
                                            <input type="text" class="layui-input search-input" name="huodongname" value="" placeholder="请输入活动名称进行搜索">
                                        </div>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-huodong">搜索</button>
                                    </div>
                                    <div class="layui-col-md12" style="margin-top:5px">
                                        <table id="huodongtabledata" lay-filter="huodongtabledata"></table>
                                    </div>
                                </div>
                            {/if}
                        {/if}

                        {if getcustom('hotel')}
                            <!-- 酒店分类标签页 -->
                            {if $auth_data=='all' || in_array('HotelCategory/*',$auth_data)}
                                <div class="layui-tab-item fe-tab-link" id="fe-tab-link-17">
                                    {foreach $hotelcatelist as $c}
                                        <div>
                                            <div class="fe-tab-link-line">
                                                <div class="fe-tab-link-sub">
                                                    <a href="javascript:void(0);" onclick="chooseLink(3,'/hotel/index/hotellist?cateid={$c.id}','{$c.name}')">选择</a>
                                                </div>
                                                <div style="float:left;">
                                                    {if $c['pic']}<img src="{$c.pic}" class="category-img"/>{/if}
                                                </div>
                                                <div class="fe-tab-link-text">
                                                    {$c.name}
                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>
                            {/if}

                            <!-- 酒店列表标签页 -->
                            {if $bid==0 && ($auth_data=='all' || in_array('Hotel/*',$auth_data))}
                                <div class="layui-tab-item fe-tab-link" id="fe-tab-link-18">
                                    <div class="layui-form layui-inline search-form">
                                        <div class="layui-input-inline">
                                            <input type="text" class="layui-input search-input" name="hotelname" value="" placeholder="请输入{$text['酒店']}名称进行搜索">
                                        </div>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-hotel">搜索</button>
                                    </div>
                                    <div class="layui-col-md12" style="margin-top:5px">
                                        <table id="hoteltabledata" lay-filter="hoteltabledata"></table>
                                    </div>
                                </div>
                            {/if}
                        {/if}
				{if getcustom('xixie')}
					{if $auth_data=='all' || in_array('XixieCategory/*',$auth_data)}

					<div class="layui-tab-item fe-tab-link" id="fe-tab-link-2">
						{foreach $xixieclist as $c}
						<div>
							<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
								<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(2,'/xixie/index?cid={$c.id}','{$c.name}')">选择</a></div>
								<div style="float:left;">
								{if $c['pic']}<img src="{$c.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
							</div>
							<div class="fe-tab-link-text" style="float:left">
								{$c.name}</div>
							</div>
						</div>
							{foreach $c['child'] as $v}
							<div style="width:95%;float:right">
								<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
									<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(2,'/xixie/index?cid={$v.id}','{$v.name}')">选择</a></div>
									<div style="float:left;">
									{if $v['pic']}<img src="{$v.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
								</div>
								<div class="fe-tab-link-text" style="float:left">
									{$v.name}</div>
								</div>
							</div>
								{foreach $v['child'] as $v2}
								<div style="width:90%;float:right">
									<div class="fe-tab-link-line" style='height:60px;line-height:60px;float:left;width:100%;'>
										<div class="fe-tab-link-sub"><a href="javascript:void(0);" onclick="chooseLink(2,'/xixie/index?cid={$v2.id}','{$v2.name}')">选择</a></div>
										<div style="float:left;">
										{if $v2['pic']}<img src="{$v2.pic}" style='width:50px;height:50px;padding:1px;border:1px solid #ccc'/>{/if}
									</div>
									<div class="fe-tab-link-text" style="float:left">
										{$v2.name}</div>
									</div>
								</div>
								{/foreach}
							{/foreach}
						{/foreach}
					</div>
					{/if}
				{/if}
				<!-- <div class="fe-tab-link" id="fe-tab-link-7">

				</div> -->
				{if $auth_data=='all' || in_array('DesignerPage/*',$auth_data) || in_array('DesignerPage/index',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-12">
						<div class="layui-form layui-inline" style="margin-top:10px">
							<div class="layui-input-inline">
									<input type="text" class="layui-input" style="height:30px;" name="designerpagename" value="" placeholder=请输入页面名称进行搜索>
							</div>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-product">搜索</button>
						</div>
						<div class="layui-col-md12" style="margin-top:5px">
							<table id="designerpagetabledata" lay-filter="designerpagetabledata"></table>
						</div>
				</div>
				{/if}
			{if $Request.param.type!='geturl'}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-13">
					<div class="layui-form" lay-filter="" style="padding-top:20px">
					<div class="layui-form-item">
						<label class="layui-form-label" style="width:100px">选择功能：</label>
						<div class="layui-input-inline">
							<input type="radio" title="拨打电话" name="linktype" value="tel" lay-filter="linktype"/>
							<input type="radio" title="小程序客服" name="linktype" value="contact" lay-filter="linktype"/>
							<input type="radio" title="分享转发" name="linktype" value="share" lay-filter="linktype"/>
							<input type="radio" title="扫一扫" name="linktype" value="scan" lay-filter="linktype"/>
							<input type="radio" title="坐标导航" name="linktype" value="location" lay-filter="linktype"/>
							{if in_array('mp',$platform)}<input type="radio" title="领取会员卡" name="linktype" value="getmembercard" lay-filter="linktype"/>{/if}
							{if in_array('wx',$platform)}<input type="radio" title="跳转其他小程序" name="linktype" value="miniProgram" lay-filter="linktype"/>{/if}
							{if in_array('wx',$platform)}<input type="radio" title="小程序激励广告" name="linktype" value="rewardedVideoAd" lay-filter="linktype"/>{/if}
							<input type="radio" title="复制文本" name="linktype" value="copy" lay-filter="linktype"/>
							<input type="radio" title="外部链接" name="linktype" value="url" lay-filter="linktype"/>
						</div>
						<div class="layui-form-mid layui-word-aux"></div>
					</div>
					<div id="linktype-content">
						<div class="layui-form-item tel" style="display:none">
							<label class="layui-form-label" style="width:100px">电话号码：</label>
							<div class="layui-input-inline">
								<input type="text" name="tel" value="" class="layui-input"/>
							</div>
						</div>
						<div class="layui-form-item contact" style="display:none">
							<label class="layui-form-label" style="width:100px"></label>
							<div class="layui-form-mid layui-word-aux">小程序在线客服仅支持以下组件：单图、按钮、悬浮按钮</div>
						</div>
						<div class="layui-form-item share" style="display:none">
							<label class="layui-form-label" style="width:100px"></label>
							<div class="layui-form-mid layui-word-aux">小程序分享转发仅支持以下组件：单图、按钮、悬浮按钮</div>
						</div>

						<div class="layui-form-item location" style="display:none">
							<label class="layui-form-label" style="width:100px">地点名称：</label>
							<div class="layui-input-inline">
								<input type="text" name="addressname" value="天安门广场" class="layui-input"/>
							</div>
						</div>
						<div class="layui-form-item location" style="display:none">
							<div class="layui-form-label" style="width:100px">经纬度：</div>
							<div class="layui-input-inline" style="width:150px">
								<input class="layui-input" name="longitude" value="116.397827"/>
							</div>
							<div class="layui-form-mid">,</div>
							<div class="layui-input-inline" style="width:150px">
								<input class="layui-input" name="latitude" value="39.90374"/>
							</div>
							<div class="layui-btn  layui-btn-primary" onclick="choosezuobiao()">选择坐标</div>
						</div>
						<div class="layui-form-item getmembercard" style="display:none">
							<label class="layui-form-label" style="width:100px"></label>
							<div class="layui-form-mid layui-word-aux">设置前请先创建微信会员卡</div>
						</div>
						<div class="layui-form-item url" style="display:none">
							<label class="layui-form-label" style="width:100px">链接地址：</label>
							<div class="layui-input-inline" style="width:300px">
								<input type="text" name="url" value="" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux" style="margin-left:130px">小程序跳转外部链接必须是https://开头的完整链接地址 可打开关联的公众号的文章，其它网页需登录<a href="https://mp.weixin.qq.com/" target="_blank">公众平台小程序账号</a>配置业务域名，个人类型与海外类型的小程序暂不支持使用。</div>
						</div>
						<div class="layui-form-item miniProgram" style="display:none">
							<label class="layui-form-label" style="width:100px">小程序 appId：</label>
							<div class="layui-input-inline">
								<input type="text" name="appid" value="" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">要跳转的小程序AppID</div>
						</div>
						<div class="layui-form-item miniProgram" style="display:none">
							<label class="layui-form-label" style="width:100px">小程序原始id：</label>
							<div class="layui-input-inline">
								<input type="text" name="username" value="" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">{if in_array('app',$platform)}APP及微信会员卡跳转小程序必填{/if}</div>
						</div>
						<div class="layui-form-item miniProgram" style="display:none">
							<label class="layui-form-label" style="width:100px">页面路径：</label>
							<div class="layui-input-inline">
								<input type="text" name="path" value="" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">不填写则默认打开首页</div>
						</div>
						<div class="layui-form-item miniProgram" style="display:none">
							<label class="layui-form-label" style="width:100px"></label>
						</div>
						<div class="layui-form-item copy" style="display:none">
							<label class="layui-form-label" style="width:100px">复制文字内容：</label>
							<div class="layui-input-inline" style="width:300px">
								<input type="text" name="copytext" value="" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux"></div>
						</div>
						<div class="layui-form-item copy" style="display:none">
							<label class="layui-form-label" style="width:100px">提示文字：</label>
							<div class="layui-input-inline" style="width:300px">
								<input type="text" name="copymsg" value="复制成功" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">复制后提示文字</div>
						</div>
						
						<div class="layui-form-item rewardedVideoAd" style="display:none">
							<label class="layui-form-label" style="width:100px">广告位ID：</label>
							<div class="layui-input-inline" style="width:300px">
								<input type="text" name="rad_unitid" value="" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">在小程序后台"流量主-广告管理-激励广告"中查找</div>
						</div>
						<div class="layui-form-item rewardedVideoAd" style="display:none">
							<label class="layui-form-label" style="width:100px">赠送积分：</label>
							<div class="layui-input-inline" style="width:300px">
								<input type="text" name="rad_givescore" value="0" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">观看结束后奖励积分数</div>
						</div>
						<div class="layui-form-item rewardedVideoAd" style="display:none">
							<label class="layui-form-label" style="width:100px">每天限制赠送次数：</label>
							<div class="layui-input-inline" style="width:300px">
								<input type="text" name="rad_givetimes" value="1" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">每天最多可赠送多少次积分</div>
						</div>
					</div>

					<div class="layui-form-item">
						<div class="layui-input-block" style="margin-left:130px;">
							<button class="layui-btn" lay-submit lay-filter="formsubmit">确 定</button>
						</div>
					</div>
					</div>
				</div>
			{/if}

				{if $auth_data=='all' || in_array('CycleProduct/*',$auth_data)}
				<div class="layui-tab-item fe-tab-link" id="fe-tab-link-14">
					<div class="layui-form layui-inline" style="margin-top:10px">
						<div class="layui-input-inline">
							<input type="text" class="layui-input" style="height:30px;" name="cycleproductname" value="" placeholder=请输入商品名称进行搜索>
						</div>
						<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit="" lay-filter="LAY-app-forumreply-search-cycleproduct">搜索</button>
					</div>
					<div class="layui-col-md12" style="margin-top:5px">
						<table id="cycleproducttabledata" lay-filter="cycleproducttabledata"></table>
					</div>
				</div>
				{/if}
		</div>
	</div>
	</div>
</div>
</div>
{include file="public/js"/}
<script>
layui.form.on('radio(linktype)', function(data){
	$('#linktype-content .layui-form-item').hide();
	$('.layui-form-item.'+data.value).show();
})

layui.form.on('submit(formsubmit)', function(obj){
	var field = obj.field
	var linktype = field.linktype
	var linkval = '';
	var hrefname = '';
	if(!linktype){
			layer.msg('请选择功能');return;
		}
	if(linktype == 'tel'){
		if(field.tel == ''){
			layer.msg('请填写电话号码');return;
		}
		linkval = field.tel
		hrefname = '拨打电话:'+field.tel;
	}
	if(linktype == 'url'){
		if(field.url == ''){
			layer.msg('请填写链接地址');return;
		}
		linkval = field.url
		hrefname = '外部链接:'+field.url;
	}
	if(linktype == 'contact'){
		hrefname = '在线客服';
	}
	if(linktype == 'scan'){
		hrefname = '扫一扫';
	}
	if(linktype == 'share'){
		hrefname = '分享转发';
	}
	if(linktype == 'location'){
		hrefname = '坐标导航';
		linkval = $('input[name=addressname]').val() + '|' + $('input[name=longitude]').val() + ','+$('input[name=latitude]').val();
	}
	if(linktype == 'getmembercard'){
		hrefname = '领取会员卡';
	}
	if(linktype == 'miniProgram'){
		if(field.appid == ''){
			//layer.msg('请填写小程序appid');return;
		}
		console.log(field)
		if(field.username){
			linkval = field.appid + '|'+field.path + '|'+field.username;
		}else{
			linkval = field.appid + '|'+field.path;
		}
		hrefname = '小程序:'+linkval;
	}
	if(linktype == 'copy'){
		hrefname = '复制';
		linkval = $('input[name=copytext]').val() + '|' + $('input[name=copymsg]').val();
	}
	if(linktype == 'rewardedVideoAd'){
		linkval = field.rad_unitid + '|'+field.rad_givescore + '|'+field.rad_givetimes;
		hrefname = '激励广告:'+field.rad_unitid;
		var index = layer.load();
		$.post("{:url('addRewardedVideoAd')}",{unitid:field.rad_unitid,givescore:field.rad_givescore,givetimes:field.rad_givetimes},function(res){
			chooseLink(13,linktype+'::'+res.adid,hrefname);
			layer.close(index);
		});
		return;
	}
	if(linktype == 'url'){
		var hrefurl = linkval;
	}else if(linktype == 'tel'){
		var hrefurl = linktype + ':' + linkval;
	}else{
		var hrefurl = linktype + '::' + linkval;
	}
	chooseLink(13,hrefurl,hrefname)
});
</script>
<script type="text/html" id="productnameTpl">
		<img src="{{d.pic}}" style="max-width:70px;float:left"/>
		<div style="float: left;width:200px;margin-left: 10px;white-space:normal;line-height:20px;">
			<div style="width:100%;">{{# if(d.cid!=0){ }}<span style="color:red">[{{d.cname}}]</span>{{# } }}{{d.name}}</div>
			<div style="padding-top:5px;color:#f60">￥{{d.sell_price}}</div></div>
		</div>
</script>
<script type="text/html" id="articlenameTpl">
		<img src="{{d.pic}}" style="max-height:50px;max-width:50px;float:left"/>
		<div style="float: left;width:200px;margin-left: 10px;white-space:normal;line-height:20px;">
			<div style="width:100%;padding-top:5px">{{# if(d.cid>0){ }}<span style="color:red">[{{d.cname}}] </span>{{# } }}{{d.name}}</div>
			<div style="padding-top:5px;font-size:12px">{{d.subname}}</div></div>
		</div>
	</script>
	<script type="text/html" id="kanjianameTpl">
		<img src="{{d.pic}}" style="max-width:70px;float:left"/>
		<div style="float: left;width:200px;margin-left: 10px;white-space:normal;line-height:20px;">
			<div style="width:100%;">{{d.name}}</div>
			<div style="color:#666;font-size:12px">原价: ￥{{d.sell_price}}</div>
			<div style="color:#f60;font-size:12px">最低价: ￥{{d.min_price}}</div>
		</div>
	</script>
	<script type="text/html" id="collagenameTpl">
		<img src="{{d.pic}}" style="max-width:70px;float:left"/>
		<div style="float: left;width:200px;margin-left: 10px;white-space:normal;line-height:20px;">
			<div style="width:100%;">{{# if(d.cid!=0){ }}<span style="color:red">[{{d.cname}}]</span>{{# } }}{{d.name}}</div>
			<div style="padding-top:5px;color:#f60">￥{{d.sell_price}}</div></div>
		</div>
	</script>
	<script type="text/html" id="scoreshopnameTpl">
		<img src="{{d.pic}}" style="max-width:70px;float:left"/>
		<div style="float: left;width:200px;margin-left: 10px;white-space:normal;line-height:20px;">
			<div style="width:100%;">{{d.name}}</div>
			<div style="color:#666;font-size:12px">价值: ￥{{d.sell_price}}</div>
			<div style="color:#f60;font-size:12px">{{d.score_price}}{:t('积分')} {{# if(d.money_price>0){ }}+ {{d.money_price}}元{{# } }}</div>
		</div>
	</script>
	<script type="text/html" id="yuyuenameTpl">
		<img src="{{d.pic}}" style="max-width:70px;float:left"/>
		<div style="float: left;width:200px;margin-left: 10px;white-space:normal;line-height:20px;">
			<div style="width:100%;">{{# if(d.cid!=0){ }}<span style="color:red">[{{d.cname}}]</span>{{# } }}{{d.name}}</div>
			<div style="padding-top:5px;color:#f60">￥{{d.sell_price}}</div></div>
		</div>
	</script>

<script>
  //商品列表
	var productdatawhere = {};
  var tableInsproduct = layui.table.render({
    elem: '#producttabledata'
    ,url: "{:url('shop_product/index')}" //数据接口
    ,page: true //开启分页
		,width:1068
		,height:480
    ,cols: [[ //表头
			//{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '商品信息',templet: '#productnameTpl',width:450},
      //{field: 'ggdata', title: '规格 × 库存'},
      {field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:150},
      {field: 'status', title: '状态',templet:function(d){ return d.status==1?'<span style="color:green">已上架</span>':'<span style="color:red">未上架</span>'},width:80},
      {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(4,\'/shopPackage/shop/product?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>'}
    ]]
  });
	//排序
	layui.table.on('sort(producttabledata)', function(obj){
		productdatawhere.field = obj.field;
		productdatawhere.order = obj.type;
		tableInsproduct.reload({
			initSort: obj,
			where: productdatawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search-product)', function(obj){
		var olddatawhere = productdatawhere
		productdatawhere = {name:obj.field.productname}
		productdatawhere.field = olddatawhere.field
		productdatawhere.order = olddatawhere.order
		console.log(productdatawhere)
		tableInsproduct.reload({
			where: productdatawhere,
			page: {curr: 1}
		});
	})


  
  //拼团商品列表
	var collageproductdatawhere = {};
  var tableInscollageproduct = layui.table.render({
    elem: '#collageproducttabledata'
    ,url: "{:url('collage_product/index')}" //数据接口
    ,page: true //开启分页
		,width:1068
		,height:480
    ,cols: [[ //表头
			//{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '商品信息',templet: '#collagenameTpl',width:450},
      //{field: 'ggdata', title: '规格 × 库存'},
      {field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:150},
      {field: 'status', title: '状态',templet:function(d){ return d.status==1?'<span style="color:green">已上架</span>':'<span style="color:red">未上架</span>'},width:80},
      {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(5,\'/activity/collage/product?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>'}
    ]]
  });
	//排序
	layui.table.on('sort(collageproducttabledata)', function(obj){
		collageproductdatawhere.field = obj.field;
		collageproductdatawhere.order = obj.type;
		tableInscollageproduct.reload({
			initSort: obj,
			where: collageproductdatawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search-collageproduct)', function(obj){
		var olddatawhere = collageproductdatawhere
		collageproductdatawhere = {name:obj.field.collageproductname}
		collageproductdatawhere.field = olddatawhere.field
		collageproductdatawhere.order = olddatawhere.order
		console.log(collageproductdatawhere)
		tableInscollageproduct.reload({
			where: collageproductdatawhere,
			page: {curr: 1}
		});
	});


	{if getcustom('huodong_baoming')}
	
	//酒店列表
	var huodongtabledatawhere = {};
	var tableInshuodong = layui.table.render({
	  elem: '#huodongtabledata'
	  ,url: "{:url('HuodongBaomingList/index')}" //数据接口
	  ,page: true //开启分页
		  ,width:1068
		  ,height:480
	  ,cols: [[ //表头
			  //{type:"checkbox"},
		{field: 'id', title: 'ID',  sort: true,width:80},
		{field: 'name', title: "活动名称",templet: '#huodongnameTpl',width:450},
		//{field: 'ggdata', title: '规格 × 库存'},
		{field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:150},
		{field: 'status', title: '状态',templet:function(d){ return d.status==1?'<span style="color:green">已上架</span>':'<span style="color:red">未上架</span>'},width:80},
		{field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(5,\'/pagesB/huodongbaoming/product?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>'}
	  ]]
	});
	  //排序
	  layui.table.on('sort(huodongtabledata)', function(obj){
		  huodongtabledatawhere.field = obj.field;
		  huodongtabledatawhere.order = obj.type;
		  tableInshuodong.reload({
			  initSort: obj,
			  where: huodongtabledatawhere
		  });
	  });
	  //检索
	  layui.form.on('submit(LAY-app-forumreply-search-huodong)', function(obj){
		  var olddatawhere = huodongtabledatawhere
		  huodongtabledatawhere = {name:obj.field.hotelname}
		  huodongtabledatawhere.field = olddatawhere.field
		  huodongtabledatawhere.order = olddatawhere.order
		  console.log(huodongtabledatawhere)
		  tableInshuodong.reload({
			  where: huodongtabledatawhere,
			  page: {curr: 1}
		  });
	  });
  
	  {/if}
  
	  
   {if getcustom('hotel')}
	  
	//酒店列表
	var hoteltabledatawhere = {};
	var tableInshotel = layui.table.render({
	  elem: '#hoteltabledata'
	  ,url: "{:url('Hotel/getlist')}" //数据接口
	  ,page: true //开启分页
		  ,width:1068
		  ,height:480
	  ,cols: [[ //表头
			  //{type:"checkbox"},
		{field: 'id', title: 'ID',  sort: true,width:80},
		{field: 'name', title: "{$text['酒店']}名称",templet: '#hotelnameTpl',width:450},
		//{field: 'ggdata', title: '规格 × 库存'},
		{field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:150},
		{field: 'status', title: '状态',templet:function(d){ return d.status==1?'<span style="color:green">已上架</span>':'<span style="color:red">未上架</span>'},width:80},
		{field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(5,\'/hotel/index/hoteldetails?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>'}
	  ]]
	});
	  //排序
	  layui.table.on('sort(hoteltabledata)', function(obj){
		  hoteltabledatawhere.field = obj.field;
		  hoteltabledatawhere.order = obj.type;
		  tableInshotel.reload({
			  initSort: obj,
			  where: hoteltabledatawhere
		  });
	  });
	  //检索
	  layui.form.on('submit(LAY-app-forumreply-search-hotel)', function(obj){
		  var olddatawhere = hoteltabledatawhere
		  hoteltabledatawhere = {name:obj.field.hotelname}
		  hoteltabledatawhere.field = olddatawhere.field
		  hoteltabledatawhere.order = olddatawhere.order
		  console.log(hoteltabledatawhere)
		  tableInshotel.reload({
			  where: hoteltabledatawhere,
			  page: {curr: 1}
		  });
	  });
  
	  {/if}
  
  //周期购商品列表
  var cycleproductdatawhere = {};
  var tableInscycleproduct = layui.table.render({
	  elem: '#cycleproducttabledata'
	  ,url: "{:url('cycle_product/index')}" //数据接口
	  ,page: true //开启分页
	  ,width:1068
	  ,height:480
	  ,cols: [[ //表头
		  //{type:"checkbox"},
		  {field: 'id', title: 'ID',  sort: true,width:80},
		  {field: 'name', title: '商品信息',templet: '#collagenameTpl',width:450},
		  //{field: 'ggdata', title: '规格 × 库存'},
		  {field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:150},
		  {field: 'status', title: '状态',templet:function(d){ return d.status==1?'<span style="color:green">已上架</span>':'<span style="color:red">未上架</span>'},width:80},
		  {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(14,\'/pagesExt/cycle/product?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>'}
	  ]]
  });
  //排序
  layui.table.on('sort(cycleproducttabledata)', function(obj){
	  collageproductdatawhere.field = obj.field;
	  collageproductdatawhere.order = obj.type;
	  tableInscollageproduct.reload({
		  initSort: obj,
		  where: collageproductdatawhere
	  });
  });
  //检索
  layui.form.on('submit(LAY-app-forumreply-search-cycleproduct)', function(obj){
	  var olddatawhere = cycleproductdatawhere
	  cycleproductdatawhere = {name:obj.field.cycleproductname}
	  cycleproductdatawhere.field = olddatawhere.field
	  cycleproductdatawhere.order = olddatawhere.order
	  tableInscycleproduct.reload({
		  where: cycleproductdatawhere,
		  page: {curr: 1}
	  });
  });
	

  //砍价商品列表
	var kanjiaproductdatawhere = {};
  var tableInskanjiaproduct = layui.table.render({
    elem: '#kanjiaproducttabledata'
    ,url: "{:url('kanjia_product/index')}" //数据接口
    ,page: true //开启分页
		,width:1068
		,height:480
    ,cols: [[ //表头
			//{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '商品信息',templet: '#kanjianameTpl',width:350},
      {field: 'sort', title: '活动时间',templet:function(d){ return date('Y-m-d H:i',d.starttime) + ' ~ '+date('Y-m-d H:i',d.endtime)},width:250},
      {field: 'status', title: '状态',templet:function(d){
				if(d.status==0){
					return '<span style="color:red">未上架</span>';
				}else{
					if(d.starttime > {:time()}){
						return '<button class="layui-btn layui-btn-sm" style="background-color:#888">未开始</button>';
					}else if(d.endtime < {:time()}){
						return '<button class="layui-btn layui-btn-sm" style="background-color:#3e5">已结束</button>';
					}else if(d.endtime > {:time()} && d.starttime < {:time()}){
						return '<button class="layui-btn layui-btn-sm" style="background-color:#f55">进行中</button>';
					}
				}
			}},
      {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(6,\'/activity/kanjia/product?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>'}
    ]]
  });
	//排序
	layui.table.on('sort(kanjiaproducttabledata)', function(obj){
		kanjiaproductdatawhere.field = obj.field;
		kanjiaproductdatawhere.order = obj.type;
		tableInskanjiaproduct.reload({
			initSort: obj,
			where: kanjiaproductdatawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search-kanjiaproduct)', function(obj){
		var olddatawhere = kanjiaproductdatawhere
		kanjiaproductdatawhere = {name:obj.field.kanjiaproductname}
		kanjiaproductdatawhere.field = olddatawhere.field
		kanjiaproductdatawhere.order = olddatawhere.order
		console.log(kanjiaproductdatawhere)
		tableInskanjiaproduct.reload({
			where: kanjiaproductdatawhere,
			page: {curr: 1}
		});
	});

	//积分商城商品列表
	var scoreshopproductdatawhere = {};
  var tableInsscoreshopproduct = layui.table.render({
    elem: '#scoreshopproducttabledata'
    ,url: "{:url('scoreshop_product/index')}" //数据接口
    ,page: true //开启分页
		,width:1068
		,height:480
    ,cols: [[ //表头
			//{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '商品信息',templet: '#scoreshopnameTpl',width:350},
      {field: 'stock', title: '库存'},
      {field: 'sales', title: '销量',sort: true},
      {field: 'sort', title: '序号',sort: true},
      {field: 'status', title: '状态',templet:function(d){
				if(d.status==0){
					return '<span style="color:red">未上架</span>';
				}else{
					return '<span style="color:green">已上架</span>';
				}
			}},
      {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(7,\'/activity/scoreshop/product?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>'}
    ]]
  });
	//排序
	layui.table.on('sort(scoreshopproducttabledata)', function(obj){
		scoreshopproductdatawhere.field = obj.field;
		scoreshopproductdatawhere.order = obj.type;
		tableInsscoreshopproduct.reload({
			initSort: obj,
			where: scoreshopproductdatawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search-scoreshopproduct)', function(obj){
		var olddatawhere = scoreshopproductdatawhere
		scoreshopproductdatawhere = {name:obj.field.scoreshopproductname}
		scoreshopproductdatawhere.field = olddatawhere.field
		scoreshopproductdatawhere.order = olddatawhere.order
		console.log(scoreshopproductdatawhere)
		tableInsscoreshopproduct.reload({
			where: scoreshopproductdatawhere,
			page: {curr: 1}
		});
	});


  //设计页面列表
	var designerpagedatawhere = {};
  var tableInsdesignerpage = layui.table.render({
    elem: '#designerpagetabledata'
    ,url: "{:url('DesignerPage/index')}/ishome/0" //数据接口
    ,page: true //开启分页
		,width:1068
		,height:480
    ,cols: [[ //表头
			//{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '页面名称',templet: '<div>{{# if(d.ishome==1){ }}<i class="fa fa-home" title="小程序首页" style="color:#fb6b5b;font-size:20px"></i> {{# } }}{{d.name}}</div>',width:403},
      //{field: 'readcount', title: '页面链接',templet: '<div>/pages/main/main?PageId={{d.id}}</div>'},
      //{field: 'sort', title: '序号',width:80,sort: true},
      //{field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:150},
      {field: 'updatetime', title: '最后修改时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.updatetime)},width:200},
      {field: 'operation', title: '操作',templet: function(d){
				var html = '';
				html += '<button class="table-btn" onclick="chooseLink(12,\'/pages/index/main?id='+d.id+'\',\''+d.name+'\')">选择</button>';
				{if input('param.fromdsn')==1}
				html += '<button class="table-btn" onclick="chooseLink(12,\'/pages/index/main?id='+d.id+'&reloadthispage=1\',\''+d.name+'\')">当前页加载</button>';
				{/if}
				return html;
      },width:200}
    ]]
  });
	//排序
	layui.table.on('sort(designerpagetabledata)', function(obj){
		designerpagedatawhere.field = obj.field;
		designerpagedatawhere.order = obj.type;
		tableInsdesignerpage.reload({
			initSort: obj,
			where: designerpagedatawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search-designerpage)', function(obj){
		//var field = obj.field
		var olddatawhere = designerpagedatawhere
		designerpagedatawhere = {name:obj.field.designerpagename}
		designerpagedatawhere.field = olddatawhere.field
		designerpagedatawhere.order = olddatawhere.order
		tableInsdesignerpage.reload({
			where: designerpagedatawhere,
			page: {curr: 1}
		});
	})
  //文章列表
	var articledatawhere = {};
  var tableInsarticle = layui.table.render({
    elem: '#articletabledata'
    ,url: "{:url('article/index')}" //数据接口
    ,page: true //开启分页
		,width:1068
		,height:480
    ,cols: [[ //表头
			//{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '文章信息',templet: '#articlenameTpl'},
      {field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:200},
      //{field: 'sendtime', title: '发布时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.sendtime)},width:120},
      {field: 'status', title: '状态',templet:function(d){ return d.status==1?'<span style="color:green">已发布</span>':'<span style="color:red">未发布</span>'},width:150},
      {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(9,\'/pagesExt/article/detail?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>',width:100}
    ]]
  });
	//排序
	layui.table.on('sort(articletabledata)', function(obj){
		articledatawhere.field = obj.field;
		articledatawhere.order = obj.type;
		tableInsarticle.reload({
			initSort: obj,
			where: articledatawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search-article)', function(obj){
		var field = obj.field
		var olddatawhere = articledatawhere
		articledatawhere = {name:obj.field.articlename}
		articledatawhere.field = olddatawhere.field
		articledatawhere.order = olddatawhere.order
		tableInsarticle.reload({
			where: articledatawhere,
			page: {curr: 1}
		});
	})
	//商家列表
	var businessdatawhere = {};
  var tableInsbusiness = layui.table.render({
    elem: '#businesstabledata'
    ,url: "{:url('business/index')}" //数据接口
    ,page: true //开启分页
		,width:1068
		,height:480
    ,cols: [[ //表头
			//{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'name', title: '商家名称'},
      {field: 'pic', title: '商家图片',templet:function(d){
				return '<img src="'+d.logo+'" style="width:60px;height:60px"/>'
      }},
      {field: 'createtime', title: '创建时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.createtime)},width:200},
      //{field: 'sendtime', title: '发布时间',sort: true,templet:function(d){ return date('Y-m-d H:i',d.sendtime)},width:120},
      {field: 'status', title: '状态',width:150,templet:function(d){
				if(d.status==1){
					return '<span style="color:green">已通过</span>'
				}else if(d.status==2){
					return '<span style="color:red">未通过</span>'
				}else{
					return '<span>审核中</span>'
				}
			}},
      {field: 'operation', title: '操作',templet: '<div><button class="table-btn" onclick="chooseLink(9,\'/pagesExt/business/index?id={{d.id}}\',\'{{d.name}}\')">选择</button></div>',width:100}
    ]]
  });
	//排序
	layui.table.on('sort(businesstabledata)', function(obj){
		businessdatawhere.field = obj.field;
		businessdatawhere.order = obj.type;
		tableInsbusiness.reload({
			initSort: obj,
			where: businessdatawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search-business)', function(obj){
		var field = obj.field
		var olddatawhere = businessdatawhere
		businessdatawhere = {name:obj.field.businessname}
		businessdatawhere.field = olddatawhere.field
		businessdatawhere.order = olddatawhere.order
		tableInsbusiness.reload({
			where: businessdatawhere,
			page: {curr: 1}
		});
	})
	
	function chooseLinkCategory(type,cid,hrefname){
		var html = '';
		html+='<div style="margin:auto auto;text-align:center">';
		html+='	<div style="display:flex;justify-content:center;margin-top:40px;color:#333;font-size:16px;">';
		html+='		<a style="margin:0 10px;width:100px;height:50px;line-height:50px;background:#fff;border:1px solid #e7e7eb;border-radius:5px;text-align:center;position:relative;cursor:pointer" onclick="chooseLink(\''+type+'\',\'/pagesExb/shop/prolist?cid='+cid+'\',\''+hrefname+'\')">商品列表</a>';
		html+='		<a style="margin:0 10px;width:100px;height:50px;line-height:50px;background:#fff;border:1px solid #e7e7eb;border-radius:5px;text-align:center;position:relative;cursor:pointer" onclick="chooseLink(\''+type+'\',\'/pagesExb/shop/classify?cid='+cid+'\',\''+hrefname+'\')">分类商品</a>';
		html+='		<div style="margin:0 10px;width:100px;height:50px;line-height:50px;background:#fff;border:1px solid #e7e7eb;border-radius:5px;text-align:center;position:relative;cursor:pointer" onclick="chooseLink(\''+type+'\',\'/pagesExb/shop/classify2?cid='+cid+'\',\''+hrefname+'\')">分类商品2</div>';
		html+='		<div style="margin:0 10px;width:100px;height:50px;line-height:50px;background:#fff;border:1px solid #e7e7eb;border-radius:5px;text-align:center;position:relative;cursor:pointer" onclick="chooseLink(\''+type+'\',\'/shopPackage/shop/fastbuy?cid='+cid+'\',\''+hrefname+'\')">快速购买</div>';
		html+='		<div style="margin:0 10px;width:100px;height:50px;line-height:50px;background:#fff;border:1px solid #e7e7eb;border-radius:5px;text-align:center;position:relative;cursor:pointer" onclick="chooseLink(\''+type+'\',\'/shopPackage/shop/fastbuy2?cid='+cid+'\',\''+hrefname+'\')">快速购买2</div>';
		html+='	</div>'
		html+='</div>'
		layer.open({type:1,area:['600px','220px'],content:html,title:'请选择链接',shadeClose:true})
	}
	function chooseLink(type,hrefurl,hrefname){
		{if $Request.param.type=='geturl'}
			var pagepath = hrefurl.substr(1);
		
			url = '{$Think.const.PRE_URL2}/h5/{$aid}.html#/'+pagepath;
			var html = '';
			html+='<div style="margin:20px">';
			html+='	<div style="width:100%;margin:10px 0" id="urlqr"></div>';
			{if in_array('wx',$platform)}
			html+='	<div style="width:100%;text-align:center"><button class="layui-btn layui-btn-sm layui-btn-primary" onclick="showwxqrcode(\''+pagepath+'\')">查看小程序码</button></div>';
			{/if}
			html+='	<div style="margin-top:30px;height:50px;line-height:25px;">链接地址：<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText(\''+url+'\')">复制</button><br>'+url+'</div>';
			html+='	<div style="height:50px;line-height:25px;">页面路径：<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText(\'/'+pagepath+'\')">复制</button><br>/'+pagepath+'</div>';
			html+='</div>'
			layer.open({type:1,'title':'链接地址',area:['600px','480px'],shadeClose:true,'content':html})
			var qrcode = new QRCode('urlqr', {
				text: 'your content',
				width: 200,
				height: 200,
				colorDark : '#000000',
				colorLight : '#ffffff',
				correctLevel : QRCode.CorrectLevel.L
			});
			qrcode.clear();
			qrcode.makeCode(url);
		{else}
			var urlname = $('#fe-tab-link-nav-' + type).text() + '>' + hrefname;
			try {
				{if input('param.callback')}
				window.parent.{$Request.param.callback}(urlname,hrefurl,"{$Request.param.args}");
				{else}
				window.parent.chooseLink(urlname,hrefurl,"{$Request.param.args}");
				{/if}
			} catch (e) {
				console.error("Error calling parent function from chooseurl.html: ", e);
			}
			closeself();
		{/if}
	}
	function showwxqrcode(pagepath){
		var index = layer.load();
		$.post("{:url('getwxqrcode')}",{path:pagepath},function(res){
			layer.close(index);
			if(res.status==0){
				//dialog(res.msg);
				layer.open({type:1,area:['300px','350px'],content:'<div style="margin:auto auto;text-align:center"><div style="color:red;width:280px;height:180px;margin-top:100px">'+res.msg+'</div><div style="height:25px;line-height:25px;">'+pagepath+'</div></div>',title:false,shadeClose:true})
			}else{
				layer.open({type:1,area:['300px','350px'],content:'<div style="margin:auto auto;text-align:center"><img src="'+res.url+'" style="margin-top:20px;max-width:280px;max-height:280px"/><div style="height:25px;line-height:25px;">'+pagepath+'</div></div>',title:false,shadeClose:true})
			}
		})
	}
	function copyText(text) {
			var textarea = document.createElement("textarea"); //创建input对象
			var currentFocus = document.activeElement; //当前获得焦点的元素
			var toolBoxwrap = document.getElementById('NewsToolBox'); //将文本框插入到NewsToolBox这个之后
			toolBoxwrap.appendChild(textarea); //添加元素
			textarea.value = text;
			textarea.focus();
			if (textarea.setSelectionRange) {
					textarea.setSelectionRange(0, textarea.value.length); //获取光标起始位置到结束位置
			} else {
					textarea.select();
			}
			try {
					var flag = document.execCommand("copy"); //执行复制
			} catch (eo) {
					var flag = false;
			}
			toolBoxwrap.removeChild(textarea); //删除元素
			currentFocus.focus();
			if(flag) layer.msg('复制成功');
			return flag;
	}

	function choosezuobiao(address,jd,wd){
		var address = $('input[name=address]').val();
		var jd = $('input[name=longitude]').val();
		var wd = $('input[name=latitude]').val();
		if(!jd){
			jd = '116.397827';
			wd = '39.90374';
		}
		var choosezblayer = layer.open({type:2,shadeClose: true,area: ['800px', '560px'],'title': '选择坐标',content: "{:url('DesignerPage/choosezuobiao')}/Mid/1&address="+(address?address:'')+'&jd='+(jd?jd:'')+'&wd='+(wd?wd:''),btn:['确定','取消'],yes:function(index, layero){
				var longitude = layero.find('iframe').contents().find('#mapjd').val();
				var latitude = layero.find('iframe').contents().find('#mapwd').val();
				$('input[name=longitude]').val(longitude);
				$('input[name=latitude]').val(latitude);
				layer.close(choosezblayer);
			}
		});
	}
</script>
<div id="NewsToolBox"></div>
<!-- <script src="__STATIC__/admin/js/clipboard.min.js"></script> -->
</body>
</html>
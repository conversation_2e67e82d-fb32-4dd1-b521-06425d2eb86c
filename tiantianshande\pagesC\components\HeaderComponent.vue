<template>
  <view class="header">
    <!-- 状态栏区域 -->
    <view class="header-status"></view>
    
    <!-- 信号区域 -->
    <view class="header-signal"></view>
    
    <!-- 标题区域 -->
    <view class="title-wrapper">
      <text class="main-title">运动健康小助手</text>
    </view>
    
    <!-- 头像区域 -->
    <view class="avatar-container">
      <view class="avatar-icon-new">💪</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HeaderComponent',
  data() {
    return {
      
    }
  },
  methods: {
    // 头像点击事件
    onAvatarClick() {
      console.log('2025-01-26 10:00:00,001-INFO-[HeaderComponent][onAvatarClick_001] 头像点击');
      // 可以添加头像点击逻辑，比如显示用户信息
      this.$emit('avatar-click');
    }
  }
}
</script>

<style scoped>
/* 头部区域 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  box-sizing: border-box;
  z-index: 999;
}

/* 状态栏 */
.header-status {
  background-color: rgba(50, 51, 53, 1);
  width: 36rpx;
  height: 24rpx;
  border-radius: 3rpx;
}

/* 信号区域 */
.header-signal {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid rgba(99, 102, 239, 1);
  margin-left: 20rpx;
}

/* 标题区域 */
.title-wrapper {
  flex: 1;
  text-align: center;
  margin: 0 30rpx;
}

.main-title {
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  letter-spacing: 1rpx;
  font-family: Microsoft YaHei UI-Bold, Microsoft YaHei, sans-serif;
  font-weight: 700;
  line-height: 32rpx;
}

/* 头像容器 */
.avatar-container {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  width: 100rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 头像图标 */
.avatar-icon-new {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(99, 102, 239, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-icon-new:hover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 239, 0.4);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header {
    height: 80rpx;
    padding: 0 16rpx;
  }

  .main-title {
    font-size: 26rpx;
  }

  .avatar-container {
    width: 88rpx;
    height: 44rpx;
  }

  .avatar-icon-new {
    width: 36rpx;
    height: 36rpx;
    font-size: 22rpx;
  }
}

@media (max-width: 480px) {
  .header {
    height: 72rpx;
    padding: 0 12rpx;
  }

  .main-title {
    font-size: 24rpx;
  }

  .header-status {
    width: 32rpx;
    height: 20rpx;
  }

  .header-signal {
    width: 36rpx;
    height: 36rpx;
    margin-left: 16rpx;
  }

  .avatar-container {
    width: 76rpx;
    height: 38rpx;
  }

  .avatar-icon-new {
    width: 30rpx;
    height: 30rpx;
    font-size: 18rpx;
  }
}
</style>
